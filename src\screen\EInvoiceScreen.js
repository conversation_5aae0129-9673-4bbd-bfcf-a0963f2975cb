import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { useLinkTo } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { prefix } from "../constant/env";
import { doc, updateDoc, collection, query, where, limit, getDocs } from "firebase/firestore";
import { Collections } from '../constant/firebase';
import { EI_STATUS } from '../constant/einvoice';

const EInvoiceScreen = props => {
    const {
        navigation,
        route,
    } = props;

    const linkTo = useLinkTo();
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    // Get order unique ID from route params
    const orderUniqueId = route?.params?.orderUniqueId;

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const orderToShow = CommonStore.useState(s => s.orderToShow);
    const selectedUserOrderTakeaway = CommonStore.useState(s => s.selectedUserOrderTakeaway);

    const userEmail = UserStore.useState(s => s.email);
    const userName = UserStore.useState(s => s.name);
    const userNumber = UserStore.useState(s => s.number);

    // Form states - using existing E-Invoice naming conventions
    const [selectedCategory, setSelectedCategory] = useState('individual');
    const [selectedSubCategory, setSelectedSubCategory] = useState('');
    const [showForm, setShowForm] = useState(false);
    const [showSuccessPage, setShowSuccessPage] = useState(false);

    // E-Invoice form states using existing naming conventions
    const [epNameToTemp, setEpNameToTemp] = useState('');
    const [epIdToTemp, setEpIdToTemp] = useState('');
    const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
    const [epTinToTemp, setEpTinToTemp] = useState('');
    const [epEmailToTemp, setEpEmailToTemp] = useState('');
    const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
    const [epAddr2ToTemp, setEpAddr2ToTemp] = useState(''); // Address Line 2
    const [epCodeToTemp, setEpCodeToTemp] = useState(''); // Postcode
    const [epCityToTemp, setEpCityToTemp] = useState('');
    const [epStateToTemp, setEpStateToTemp] = useState('Malaysia');

    // Auto-fill form with existing user data from crmUser
    useEffect(() => {
        const loadUserData = () => {
            console.log('Loading user data for E-Invoice form...');
            console.log('global.crmUser:', global.crmUser);

            // Check if user is logged in and has crmUser data
            if (global.crmUser) {
                const crmUser = global.crmUser;

                console.log('Found crmUser data:', {
                    epNameTo: crmUser.epNameTo,
                    eiId: crmUser.eiId,
                    epPhoneTo: crmUser.epPhoneTo,
                    tin: crmUser.tin,
                    emailSecond: crmUser.emailSecond,
                    epAddr1To: crmUser.epAddr1To,
                    epAddr2To: crmUser.epAddr2To,
                    epCodeTo: crmUser.epCodeTo,
                    epCityTo: crmUser.epCityTo,
                    epStateTo: crmUser.epStateTo
                });

                // Use crmUser E-Invoice data if available, otherwise use current user data
                setEpNameToTemp(crmUser.epNameTo || userName || '');
                setEpIdToTemp(crmUser.eiId || '');
                setEpPhoneToTemp(crmUser.epPhoneTo || userNumber || '');
                setEpTinToTemp(crmUser.tin || '');
                setEpEmailToTemp(crmUser.emailSecond || userEmail || '');
                setEpAddr1ToTemp(crmUser.epAddr1To || '');
                setEpAddr2ToTemp(crmUser.epAddr2To || '');
                setEpCodeToTemp(crmUser.epCodeTo || '');
                setEpCityToTemp(crmUser.epCityTo || '');
                setEpStateToTemp(crmUser.epStateTo || 'Malaysia');

                console.log('Auto-filled E-Invoice form with values:', {
                    name: crmUser.epNameTo || userName || '',
                    id: crmUser.eiId || '',
                    phone: crmUser.epPhoneTo || userNumber || '',
                    tin: crmUser.tin || '',
                    email: crmUser.emailSecond || userEmail || '',
                    addr1: crmUser.epAddr1To || '',
                    addr2: crmUser.epAddr2To || '',
                    postcode: crmUser.epCodeTo || '',
                    city: crmUser.epCityTo || '',
                    state: crmUser.epStateTo || 'Malaysia'
                });
            } else {
                // Fallback to current user data if no crmUser
                setEpNameToTemp(userName || '');
                setEpPhoneToTemp(userNumber || '');
                setEpEmailToTemp(userEmail || '');

                console.log('No crmUser found, using basic user data:', {
                    name: userName || '',
                    phone: userNumber || '',
                    email: userEmail || ''
                });
            }
        };

        loadUserData();
    }, [userName, userNumber, userEmail]);

    navigation.setOptions({
        headerLeft: () => null, // Remove back button from header
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -1,
            }}>
                <Text style={{
                    fontSize: 20,
                    lineHeight: 25,
                    textAlign: 'center',
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.mainTxtColor,
                }}>
                    {showSuccessPage ? 'E-Invoice Generated' : showForm ? 'My Profile Form' : 'E-Invoice Request'}
                </Text>
            </View>
        ),
        headerTintColor: "#000000",
    });

    // Input change handlers using existing E-Invoice naming conventions
    const handleInputChange = (field, value) => {
        switch (field) {
            case 'name':
                setEpNameToTemp(value);
                break;
            case 'idNumber':
                setEpIdToTemp(value);
                break;
            case 'mobileNumber':
                setEpPhoneToTemp(value);
                break;
            case 'sstNumber':
                setEpTinToTemp(value);
                break;
            case 'email':
                setEpEmailToTemp(value);
                break;
            case 'addressLine1':
                setEpAddr1ToTemp(value);
                break;
            case 'addressLine2':
                setEpAddr2ToTemp(value);
                break;
            case 'postcode':
                setEpCodeToTemp(value);
                break;
            case 'city':
                setEpCityToTemp(value);
                break;
            case 'country':
                setEpStateToTemp(value);
                break;
            default:
                break;
        }
    };

    const handleCategorySelect = (category, subCategory) => {
        setSelectedCategory(category);
        setSelectedSubCategory(subCategory);
        setShowForm(true);
    };

    // Validation functions based on existing codebase patterns
    const validatePhoneNumber = (phone) => {
        const phoneNumParsed = phone.replace(/[^0-9]/g, '');

        if (phoneNumParsed && !(phoneNumParsed.length === 10 || phoneNumParsed.length === 11)) {
            if (
                (phoneNumParsed.startsWith('011') && phoneNumParsed.length === 11) ||
                (phoneNumParsed.startsWith('6011') && phoneNumParsed.length === 12)
            ) {
                return true; // valid
            } else {
                return false; // invalid
            }
        }
        return phoneNumParsed.length >= 10; // basic validation
    };

    const validateEmail = (email) => {
        if (!email) return true; // email is optional
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const validatePostcode = (postcode) => {
        const postcodeRegex = /^\d{5}$/; // Malaysian postcode format
        return postcodeRegex.test(postcode);
    };

    const handleContinue = async () => {
        // Validate required fields using existing E-Invoice field names
        const requiredFieldsData = {
            name: epNameToTemp,
            idNumber: epIdToTemp,
            mobileNumber: epPhoneToTemp,
            addressLine1: epAddr1ToTemp,
            postcode: epCodeToTemp,
            city: epCityToTemp,
        };

        const missingFields = Object.entries(requiredFieldsData)
            .filter(([, value]) => !value || !value.trim())
            .map(([key]) => key);

        if (missingFields.length > 0) {
            alert('Incorrect Information - Please check and submit your form again');
            return;
        }

        // Validate phone number format
        if (!validatePhoneNumber(epPhoneToTemp)) {
            alert('Incorrect Information - Invalid phone number format.\neg: 60123456789.');
            return;
        }

        // Validate email format if provided
        if (epEmailToTemp && !validateEmail(epEmailToTemp)) {
            alert('Incorrect Information - Please enter a valid email address.');
            return;
        }

        // Validate postcode format
        if (!validatePostcode(epCodeToTemp)) {
            alert('Incorrect Information - Please enter a valid 5-digit postcode.');
            return;
        }

        // Prepare E-Invoice data for API submission
        const eInvoiceData = {
            epNameTo: epNameToTemp,
            epIdTo: epIdToTemp,
            eiId: epIdToTemp, // Use eiId field name as in crmUser
            epPhoneTo: epPhoneToTemp.startsWith('6') ? epPhoneToTemp : `6${epPhoneToTemp}`,
            tin: epTinToTemp,
            emailSecond: epEmailToTemp,
            epAddr1To: epAddr1ToTemp,
            epAddr2To: epAddr2ToTemp,
            epCodeTo: epCodeToTemp,
            epCityTo: epCityToTemp,
            epStateTo: epStateToTemp,
            orderUniqueId: orderUniqueId,
            selectedCategory: selectedCategory,
            selectedSubCategory: selectedSubCategory,
        };

        console.log('E-Invoice Data prepared for submission:', eInvoiceData);

        // Update crmUser with E-Invoice data if user is logged in
        if (global.crmUser && global.crmUser.uniqueId) {
            try {
                const crmUserRef = doc(global.db, Collections.CRMUser, global.crmUser.uniqueId);

                // Prepare update data with both E-Invoice fields and basic user info
                const updateData = {
                    // E-Invoice specific fields
                    epNameTo: epNameToTemp,
                    eiId: epIdToTemp,
                    epPhoneTo: epPhoneToTemp.startsWith('6') ? epPhoneToTemp : `6${epPhoneToTemp}`,
                    tin: epTinToTemp,
                    emailSecond: epEmailToTemp,
                    epAddr1To: epAddr1ToTemp,
                    epAddr2To: epAddr2ToTemp,
                    epCodeTo: epCodeToTemp,
                    epCityTo: epCityToTemp,
                    epStateTo: epStateToTemp,

                    // Update basic user information as well
                    name: epNameToTemp, // Update user's name
                    number: epPhoneToTemp.startsWith('6') ? epPhoneToTemp : `6${epPhoneToTemp}`, // Update phone number
                    email: epEmailToTemp || global.crmUser.email, // Update email if provided

                    updatedAt: Date.now(),
                };

                await updateDoc(crmUserRef, updateData);

                // Update global.crmUser with new data
                global.crmUser = {
                    ...global.crmUser,
                    ...updateData,
                };

                // Also update UserStore to reflect changes in the app
                UserStore.update(s => {
                    s.name = epNameToTemp;
                    s.number = epPhoneToTemp.startsWith('6') ? epPhoneToTemp : `6${epPhoneToTemp}`;
                    if (epEmailToTemp) {
                        s.email = epEmailToTemp;
                    }
                    s.epNameTo = epNameToTemp;
                    s.epPhoneTo = epPhoneToTemp.startsWith('6') ? epPhoneToTemp : `6${epPhoneToTemp}`;
                    s.epIdTo = epIdToTemp;
                    s.epTinTo = epTinToTemp;
                    s.epEmailTo = epEmailToTemp;
                    s.epAddr1To = epAddr1ToTemp;
                    s.epAddr2To = epAddr2ToTemp;
                    s.epCodeTo = epCodeToTemp;
                    s.epCityTo = epCityToTemp;
                    s.epStateTo = epStateToTemp;
                });

                console.log('Successfully updated crmUser and UserStore with E-Invoice data');
            } catch (error) {
                console.error('Error updating crmUser:', error);
                alert('Error saving your information. Please try again.');
                return;
            }
        }

        // Show success page
        setShowSuccessPage(true);
    };

    const handleBack = async () => {
        if (showSuccessPage) {
            // From success page, try to go back or navigate to menu
            try {
                // Try to go back to previous page
                if (window.history.length > 1) {
                    linkTo && linkTo(-1);
                } else {
                    // No history, navigate to menu
                    const subdomain = await AsyncStorage.getItem("latestSubdomain");
                    if (!subdomain) {
                        linkTo && linkTo(`${prefix}/outlet/menu`);
                    } else {
                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                    }
                }
            } catch (error) {
                console.error('Error navigating back:', error);
                // Fallback to menu navigation
                const subdomain = await AsyncStorage.getItem("latestSubdomain");
                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                } else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }
        } else if (showForm) {
            setShowForm(false);
        } else {
            // From category selection page, try to go back or navigate to menu
            try {
                if (window.history.length > 1) {
                    linkTo && linkTo(-1);
                } else {
                    // No history, navigate to menu
                    const subdomain = await AsyncStorage.getItem("latestSubdomain");
                    if (!subdomain) {
                        linkTo && linkTo(`${prefix}/outlet/menu`);
                    } else {
                        linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                    }
                }
            } catch (error) {
                console.error('Error navigating back:', error);
                // Fallback to menu navigation
                const subdomain = await AsyncStorage.getItem("latestSubdomain");
                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                } else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }
        }
    };

    // State for storing fetched order data
    const [fetchedOrderData, setFetchedOrderData] = useState(null);
    const [isLoadingOrderData, setIsLoadingOrderData] = useState(false);

    // Fetch order data from userorder collection using uniqueId
    const fetchOrderDataFromFirestore = async (uniqueId) => {
        if (!uniqueId || !global.db) {
            console.log('No uniqueId or database connection available');
            return null;
        }

        setIsLoadingOrderData(true);
        try {
            console.log('Fetching order data for uniqueId:', uniqueId);

            // Query userorder collection by uniqueId
            const orderQuery = query(
                collection(global.db, Collections.UserOrder),
                where('uniqueId', '==', uniqueId),
                limit(1)
            );

            const orderSnapshot = await getDocs(orderQuery);

            if (!orderSnapshot.empty) {
                const orderDoc = orderSnapshot.docs[0];
                const orderData = orderDoc.data();

                console.log('Found order data:', {
                    receiptId: orderData.receiptId,
                    createdAt: orderData.createdAt,
                    finalPrice: orderData.finalPrice,
                    uniqueId: orderData.uniqueId
                });

                setFetchedOrderData(orderData);
                return orderData;
            } else {
                console.log('No order found with uniqueId:', uniqueId);
                return null;
            }
        } catch (error) {
            console.error('Error fetching order data:', error);
            return null;
        } finally {
            setIsLoadingOrderData(false);
        }
    };

    // Fetch order data when component mounts or orderUniqueId changes
    useEffect(() => {
        if (orderUniqueId && showSuccessPage) {
            fetchOrderDataFromFirestore(orderUniqueId);
        }
    }, [orderUniqueId, showSuccessPage]);

    // Get E-Invoice status information
    const getEInvoiceStatus = (order) => {
        const eiStatus = order?.eiStatus;

        // Default status if no E-Invoice status is set
        if (!eiStatus || eiStatus === EI_STATUS.NONE) {
            return {
                status: 'Pending',
                color: '#FF9500', // Orange for pending
                backgroundColor: '#FFF3E0'
            };
        }

        // Map E-Invoice status to display information
        switch (eiStatus) {
            case EI_STATUS.VALID:
                return {
                    status: 'Valid',
                    color: '#4CAF50', // Green for valid
                    backgroundColor: '#E8F5E8'
                };
            case EI_STATUS.CANCELLED:
                return {
                    status: 'Cancelled',
                    color: '#F44336', // Red for cancelled
                    backgroundColor: '#FFEBEE'
                };
            case EI_STATUS.SUBMITTED:
                return {
                    status: 'Submitted',
                    color: '#2196F3', // Blue for submitted
                    backgroundColor: '#E3F2FD'
                };
            case EI_STATUS.PENDING:
                return {
                    status: 'Pending',
                    color: '#FF9500', // Orange for pending
                    backgroundColor: '#FFF3E0'
                };
            case EI_STATUS.EXPORTED:
                return {
                    status: 'Exported',
                    color: '#9C27B0', // Purple for exported
                    backgroundColor: '#F3E5F5'
                };
            case EI_STATUS.INVALID:
                return {
                    status: 'Invalid',
                    color: '#F44336', // Red for invalid
                    backgroundColor: '#FFEBEE'
                };
            default:
                return {
                    status: 'Pending',
                    color: '#FF9500', // Orange for default
                    backgroundColor: '#FFF3E0'
                };
        }
    };

    // Get order and outlet data for success page
    const getOrderData = () => {
        // Use fetched order data first, then fallback to existing data
        const order = fetchedOrderData || selectedUserOrderTakeaway || orderToShow;
        const statusInfo = getEInvoiceStatus(order);

        return {
            storeName: selectedOutlet?.name || 'N/A',
            date: order?.createdAt ?
                new Date(order.createdAt).toLocaleString('en-GB', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(',', '') :
                'N/A',
            receiptNumber: order?.receiptId || 'N/A',
            total: (order?.finalPrice !== undefined && order?.finalPrice !== null) ? `RM ${order.finalPrice.toFixed(2)}` : 'N/A',
            isLoading: isLoadingOrderData,
            eiStatus: statusInfo
        };
    };

    const renderCategorySelection = () => (
        <View style={styles.section}>
            <Text style={styles.sectionTitle}>Category</Text>

            <Text style={styles.categoryHeader}>Claiming as an individual</Text>

            <TouchableOpacity
                style={styles.categoryItem}
                onPress={() => handleCategorySelect('individual', 'malaysian')}
            >
                <Text style={styles.categoryText}>Malaysian</Text>
                <Ionicons name="chevron-forward" size={20} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>

            <TouchableOpacity
                style={styles.categoryItem}
                onPress={() => handleCategorySelect('individual', 'non-malaysian')}
            >
                <Text style={styles.categoryText}>Non-Malaysian</Text>
                <Ionicons name="chevron-forward" size={20} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>

            <Text style={styles.categoryHeader}>Claiming as a business</Text>

            <TouchableOpacity
                style={styles.categoryItem}
                onPress={() => handleCategorySelect('business', 'business')}
            >
                <Text style={styles.categoryText}>Business</Text>
                <Ionicons name="chevron-forward" size={20} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>
        </View>
    );

    const renderForm = () => (
        <View style={styles.section}>
            {/* Name Field */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    Name (As per MyKad/MyTentera) <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                    style={styles.textInput}
                    value={epNameToTemp}
                    onChangeText={(text) => handleInputChange('name', text)}
                    placeholder="Enter your name"
                />
            </View>



            {/* ID Number Field */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    MyKad/MyTentera Number <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                    style={styles.textInput}
                    value={epIdToTemp}
                    onChangeText={(text) => handleInputChange('idNumber', text)}
                    placeholder="Enter MyKad/MyTentera number"
                />
            </View>

            {/* Mobile Number Field */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    Mobile Number <Text style={styles.required}>*</Text>
                </Text>
                <View style={styles.phoneInputContainer}>
                    <View style={styles.countryCode}>
                        <Text style={styles.countryCodeText}>🇲🇾 +60</Text>
                    </View>
                    <TextInput
                        style={[styles.textInput, styles.phoneInput]}
                        value={epPhoneToTemp}
                        onChangeText={(text) => handleInputChange('mobileNumber', text)}
                        placeholder="Enter mobile number"
                        keyboardType="phone-pad"
                    />
                </View>
            </View>

            {/* SST Registration Number Field */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>SST Registration Number</Text>
                <TextInput
                    style={styles.textInput}
                    value={epTinToTemp}
                    onChangeText={(text) => handleInputChange('sstNumber', text)}
                    placeholder="Enter SST registration number"
                />
            </View>

            {/* Email Field */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Email</Text>
                <TextInput
                    style={styles.textInput}
                    value={epEmailToTemp}
                    onChangeText={(text) => handleInputChange('email', text)}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                />
            </View>

            {/* Billing Address Section */}
            <Text style={styles.sectionSubTitle}>Billing Address</Text>

            {/* Address Line 1 */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    Address Line 1 <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                    style={styles.textInput}
                    value={epAddr1ToTemp}
                    onChangeText={(text) => handleInputChange('addressLine1', text)}
                    placeholder="Enter address line 1"
                />
            </View>

            {/* Address Line 2 */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Address Line 2</Text>
                <TextInput
                    style={styles.textInput}
                    value={epAddr2ToTemp}
                    onChangeText={(text) => handleInputChange('addressLine2', text)}
                    placeholder="Enter address line 2"
                />
            </View>

            {/* Postcode */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    Postcode <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                    style={styles.textInput}
                    value={epCodeToTemp}
                    onChangeText={(text) => handleInputChange('postcode', text)}
                    placeholder="Enter postcode"
                    keyboardType="numeric"
                />
            </View>

            {/* City */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    City <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                    style={styles.textInput}
                    value={epCityToTemp}
                    onChangeText={(text) => handleInputChange('city', text)}
                    placeholder="Enter city"
                />
            </View>

            {/* Country */}
            <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>
                    Country <Text style={styles.required}>*</Text>
                </Text>
                <TouchableOpacity style={styles.dropdownContainer}>
                    <Text style={styles.dropdownText}>{epStateToTemp}</Text>
                    <Ionicons name="chevron-down" size={20} color={Colors.fieldtTxtColor} />
                </TouchableOpacity>
            </View>

            {/* Continue Button */}
            <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
                <Text style={styles.continueButtonText}>CONTINUE</Text>
            </TouchableOpacity>
        </View>
    );

    const renderSuccessPage = () => {
        const orderData = getOrderData();

        return (
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>KooDoo e-Invoice</Text>

                {/* E-Invoice Details */}
                <View style={styles.detailsContainer}>
                    <Text style={styles.detailsTitle}>e-Invoice Details</Text>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Status</Text>
                        <View style={[styles.statusBadge, { backgroundColor: orderData.eiStatus.color }]}>
                            <Text style={styles.statusText}>{orderData.eiStatus.status}</Text>
                        </View>
                    </View>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Reference Number</Text>
                        <Text style={styles.detailValue}>MACHC6ECVQPK5PZTB6ZRDHZH10</Text>
                    </View>
                </View>

                {/* Store Receipt Details */}
                <View style={styles.detailsContainer}>
                    <Text style={styles.detailsTitle}>Store Receipt Details</Text>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Store Name</Text>
                        <Text style={styles.detailValue}>{orderData.storeName}</Text>
                    </View>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Date</Text>
                        <Text style={styles.detailValue}>
                            {orderData.isLoading ? 'Loading...' : orderData.date}
                        </Text>
                    </View>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Receipt Number</Text>
                        <Text style={styles.detailValue}>
                            {orderData.isLoading ? 'Loading...' : orderData.receiptNumber}
                        </Text>
                    </View>

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Total</Text>
                        <Text style={styles.detailValue}>
                            {orderData.isLoading ? 'Loading...' : orderData.total}
                        </Text>
                    </View>
                </View>

                {/* Action Buttons */}
                <TouchableOpacity style={styles.viewInvoiceButton}>
                    <Text style={styles.viewInvoiceButtonText}>VIEW E-INVOICE</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.viewStatusButton}>
                    <Text style={styles.viewStatusButtonText}>VIEW STATUS ON IRBM</Text>
                </TouchableOpacity>
            </View>
        );
    };

    // Create responsive styles
    const getResponsiveStyles = () => {
        const isTablet = windowWidth > 768;

        return StyleSheet.create({
            container: {
                flex: 1,
                backgroundColor: Colors.whiteColor,
            },
            header: {
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: isTablet ? windowWidth * 0.1 : 20,
                paddingVertical: 15,
                borderBottomWidth: 1,
                borderBottomColor: Colors.fieldtBgColor,
            },
            backButton: {
                flexDirection: 'row',
                alignItems: 'center',
            },
            backButtonText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
                marginLeft: 8,
            },
            scrollViewContent: {
                paddingHorizontal: isTablet ? windowWidth * 0.1 : 20,
                paddingTop: 20,
                paddingBottom: 50,
            },
            section: {
                marginVertical: 10,
                maxWidth: isTablet ? 600 : '100%',
                alignSelf: 'center',
                width: '100%',
                minHeight: 400,
            },
            sectionTitle: {
                fontSize: isTablet ? 20 : 18,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
                marginBottom: 20,
                textAlign: isTablet ? 'center' : 'left',
            },
            categoryHeader: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
                marginBottom: 10,
                marginTop: 20,
            },
            categoryItem: {
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingVertical: isTablet ? 18 : 15,
                paddingHorizontal: isTablet ? 25 : 20,
                backgroundColor: Colors.fieldtBgColor,
                borderRadius: 10,
                marginBottom: 10,
                minHeight: 50,
            },
            selectedCategory: {
                backgroundColor: Colors.highlightColor,
                borderColor: Colors.primaryColor,
                borderWidth: 1,
            },
            categoryText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
            },
            sectionSubTitle: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
                marginBottom: 15,
                marginTop: 20,
            },
            inputContainer: {
                marginBottom: isTablet ? 20 : 15,
            },
            inputLabel: {
                fontSize: isTablet ? 16 : 14,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
                marginBottom: 8,
            },
            required: {
                color: Colors.tabRed,
            },
            textInput: {
                borderWidth: 1,
                borderColor: Colors.fieldtTxtColor,
                borderRadius: 8,
                paddingHorizontal: isTablet ? 18 : 15,
                paddingVertical: isTablet ? 15 : 12,
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Regular',
                backgroundColor: Colors.whiteColor,
                color: Colors.mainTxtColor,
                minHeight: isTablet ? 50 : 44,
            },
            phoneInputContainer: {
                flexDirection: 'row',
                alignItems: 'center',
            },
            countryCode: {
                borderWidth: 1,
                borderColor: Colors.fieldtTxtColor,
                borderRadius: 8,
                paddingHorizontal: isTablet ? 18 : 15,
                paddingVertical: isTablet ? 15 : 12,
                marginRight: 10,
                backgroundColor: Colors.fieldtBgColor,
                minHeight: isTablet ? 50 : 44,
                justifyContent: 'center',
            },
            countryCodeText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
            },
            phoneInput: {
                flex: 1,
            },
            dropdownContainer: {
                borderWidth: 1,
                borderColor: Colors.fieldtTxtColor,
                borderRadius: 8,
                paddingHorizontal: isTablet ? 18 : 15,
                paddingVertical: isTablet ? 15 : 12,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                backgroundColor: Colors.whiteColor,
                minHeight: isTablet ? 50 : 44,
            },
            dropdownText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
            },
            continueButton: {
                backgroundColor: Colors.primaryColor,
                borderRadius: 8,
                paddingVertical: isTablet ? 18 : 15,
                alignItems: 'center',
                marginTop: 30,
                marginBottom: 20,
                minHeight: isTablet ? 55 : 50,
                justifyContent: 'center',
            },
            continueButtonText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.whiteColor,
            },
            dropdownList: {
                backgroundColor: Colors.whiteColor,
                borderWidth: 1,
                borderColor: Colors.fieldtTxtColor,
                borderRadius: 8,
                marginTop: 5,
                maxHeight: 150,
            },
            dropdownItem: {
                paddingVertical: isTablet ? 15 : 12,
                paddingHorizontal: isTablet ? 18 : 15,
                borderBottomWidth: 1,
                borderBottomColor: Colors.fieldtBgColor,
            },
            dropdownItemText: {
                fontSize: isTablet ? 16 : 14,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
            },
            // Success page styles
            detailsContainer: {
                backgroundColor: Colors.whiteColor,
                borderRadius: 8,
                padding: isTablet ? 20 : 15,
                marginBottom: 20,
                borderWidth: 1,
                borderColor: Colors.fieldtBgColor,
            },
            detailsTitle: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.mainTxtColor,
                marginBottom: 15,
            },
            detailRow: {
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 12,
            },
            detailLabel: {
                fontSize: isTablet ? 16 : 14,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
                flex: 1,
            },
            detailValue: {
                fontSize: isTablet ? 16 : 14,
                fontFamily: 'NunitoSans-Regular',
                color: Colors.mainTxtColor,
                flex: 2,
                textAlign: 'right',
            },
            statusBadge: {
                paddingHorizontal: 12,
                paddingVertical: 4,
                borderRadius: 12,
            },
            statusText: {
                fontSize: isTablet ? 14 : 12,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.whiteColor,
            },
            viewInvoiceButton: {
                backgroundColor: Colors.primaryColor,
                paddingVertical: isTablet ? 15 : 12,
                borderRadius: 8,
                marginBottom: 15,
                alignItems: 'center',
            },
            viewInvoiceButtonText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.whiteColor,
            },
            viewStatusButton: {
                backgroundColor: 'transparent',
                paddingVertical: isTablet ? 15 : 12,
                borderRadius: 8,
                borderWidth: 2,
                borderColor: Colors.primaryColor,
                alignItems: 'center',
            },
            viewStatusButtonText: {
                fontSize: isTablet ? 18 : 16,
                fontFamily: 'NunitoSans-Bold',
                color: Colors.primaryColor,
            },
        });
    };

    const styles = getResponsiveStyles();

    return (
        <View style={styles.container}>
            {/* Header with Back Button */}
            <View style={styles.header}>
                <TouchableOpacity onPress={handleBack} style={styles.backButton}>
                    <Ionicons name="chevron-back" size={24} color={Colors.mainTxtColor} />
                    <Text style={styles.backButtonText}>
                        {showSuccessPage ? 'Back' : showForm ? 'My Profile Form' : 'E-Invoice Request'}
                    </Text>
                </TouchableOpacity>
            </View>

            <ScrollView
                showsVerticalScrollIndicator={true}
                contentContainerStyle={styles.scrollViewContent}
                style={{ height: windowHeight * 0.9 }}
            >
                {showSuccessPage ? renderSuccessPage() : showForm ? renderForm() : renderCategorySelection()}
            </ScrollView>
        </View>
    );
};

export default EInvoiceScreen;
