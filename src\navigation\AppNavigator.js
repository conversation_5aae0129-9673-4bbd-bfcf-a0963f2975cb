import React, { Component, useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  // useWindowDimensions,
  StyleSheet,
  Dimensions,
} from "react-native";
import { NavigationContainer, useLinkTo } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createStackNavigator, useHeaderHeight } from "@react-navigation/stack";
import loadable from '@loadable/component';
// import OrderReceiptScreen from "../screen/OrderReceiptScreen";
// import LoyaltyCampaignListScreen from "../screen/LoyaltyCampaignListScreen";
// import LoyaltyCampaignDetailsScreen from "../screen/LoyaltyCampaignDetailsScreen";
import { prefix } from "../constant/env";
import { lazyRetry } from "../util/commonFuncs";
// import VoucherScreen from "../screen/VoucherScreen";
// import VoucherHistory from "../screen/VoucherHistory";

const TakeawayScreen = loadable(() => lazyRetry(() => import("../screen/TakeawayScreen")));
const DineInScreen = loadable(() => lazyRetry(() => import("../screen/DineInScreen")));
const DeliveryScreen = loadable(() => lazyRetry(() => import("../screen/DeliveryScreen")));
const NewOrderScreen = loadable(() => lazyRetry(() => import("../screen/NewOrderScreen")));
const NewOrderGenericScreen = loadable(() => lazyRetry(() => import("../screen/NewOrderGenericScreen")));
const ScanScreen = loadable(() => lazyRetry(() => import("../screen/ScanScreen")));
const ErrorScreen = loadable(() => lazyRetry(() => import("../screen/ErrorScreen")));
const PaymentSuccessScreen = loadable(() => lazyRetry(() => import("../screen/PaymentSuccessScreen")));
const PaymentCreditSuccessScreen = loadable(() => lazyRetry(() => import("../screen/PaymentCreditSuccessScreen")));
const OutletMenuScreen = loadable(() => lazyRetry(() => import("../screen/OutletMenuScreen")));
const MenuItemDetailsScreen = loadable(() => lazyRetry(() => import("../screen/MenuItemDetailsScreen")));
const OrderHistoryDetail = loadable(() => lazyRetry(() => import("../screen/OrderHistoryDetail")));
const OrderHistoryDetailTakeaway = loadable(() => lazyRetry(() => import("../screen/OrderHistoryDetailTakeaway")));
const OutletScreen = loadable(() => lazyRetry(() => import("../screen/OutletScreen")));
const CartScreen = loadable(() => lazyRetry(() => import("../screen/CartScreen")));
const CartTaggableVoucherScreen = loadable(() => lazyRetry(() => import("../screen/CartTaggableVoucherScreen")));
const CartTopupCreditScreen = loadable(() => lazyRetry(() => import("../screen/CartTopupCreditScreen")));
const CartPayHybridScreen = loadable(() => lazyRetry(() => import("../screen/CartPayHybridScreen")));
const CartNoVoucherScreen = loadable(() => lazyRetry(() => import("../screen/CartNoVoucherScreen")));
const ReservationScreen = loadable(() => lazyRetry(() => import("../screen/ReservationScreen")));
const AddressScreen = loadable(() => lazyRetry(() => import("../screen/AddressScreen")));
const HomeAddress = loadable(() => lazyRetry(() => import("../screen/HomeAddress")));
const EditAddress = loadable(() => lazyRetry(() => import("../screen/EditAddress")));
const NewAddress = loadable(() => lazyRetry(() => import("../screen/NewAddress")));
const WorkAddress = loadable(() => lazyRetry(() => import("../screen/WorkAddress")));
const AddAddress = loadable(() => lazyRetry(() => import("../screen/AddAddress")));
const TaggableVoucherListScreen = loadable(() => lazyRetry(() => import("../screen/TaggableVoucherListScreen")));
const TaggableVoucherListPurchaseScreen = loadable(() => lazyRetry(() => import("../screen/TaggableVoucherListPurchaseScreen")));
const TaggableVoucherDetailsScreen = loadable(() => lazyRetry(() => import("../screen/TaggableVoucherDetailsScreen")));
const OrderNotificationListScreen = loadable(() => lazyRetry(() => import("../screen/OrderNotificationListScreen")));
const LoyaltySignUp = loadable(() => lazyRetry(() => import("../screen/LoyaltySignUp")));
const LoyaltySignUpOutlet = loadable(() => lazyRetry(() => import("../screen/LoyaltySignUpOutlet")));
const LoyaltyHistory = loadable(() => lazyRetry(() => import("../screen/LoyaltyHistory")));
const ReservationSummary = loadable(() => lazyRetry(() => import("../screen/ReservationSummary")));
const ReservationDetailsScreen = loadable(() => lazyRetry(() => import("../screen/ReservationDetailsScreen")));
const ClaimVoucher = loadable(() => lazyRetry(() => import("../screen/ClaimVoucher")));
const CreateQueueScreen = loadable(() => lazyRetry(() => import("../screen/CreateQueueScreen")));
const ConfirmQueueScreen = loadable(() => lazyRetry(() => import("../screen/ConfirmQueueScreen")));
const QueueScreen = loadable(() => lazyRetry(() => import("../screen/QueueScreen")));
const QueueSuccessfulScreen = loadable(() => lazyRetry(() => import("../screen/QueueSuccessfulScreen")));
const OutletRewardScreen = loadable(() => lazyRetry(() => import('../screen/OutletRewardScreen')));
const UpsellingAfterCartScreen = loadable(() => lazyRetry(() => import('../screen/UpsellingAfterCartScreen')));
const UpsellingAfterCheckoutScreen = loadable(() => lazyRetry(() => import('../screen/UpsellingAfterCheckoutScreen')));
const ReservationUpsellingScreen = loadable(() => lazyRetry(() => import('../screen/ReservationUpsellingScreen')));
const RewardDetailsScreen = loadable(() => lazyRetry(() => import('../screen/RewardDetailsScreen')));
const OR_StampDetails = loadable(() => lazyRetry(() => import('../screen/OR_StampDetails')));
const CreditDetailsScreen = loadable(() => lazyRetry(() => import('../screen/CreditDetailsScreen')));
const EInvoiceScreen = loadable(() => lazyRetry(() => import('../screen/EInvoiceScreen')));
const OutletPreviewScreen = loadable(() => lazyRetry(() => import('../screen/OutletPreviewScreen')));
const OrderQueueScreen = loadable(() => lazyRetry(() => import('../screen/OrderQueueScreen')));

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const config = {
  // screens: {
  //   MainScreen: {
  //     path: 'qr/:outletId/:tableId/:tableCode/:waiterId?',
  //   },
  // },
  screens: {
    // KooDooOrder: {
    //   path: "/web/:outletId/:tableId/:tableCode/:tablePax/:waiterId",
    //   initialRouteName: "neworder",
    //   screens: {
    //     Login: {
    //       path: 'new-order',
    //     },
    //   },
    // },    
    'KooDooOrder': {
      path: `${prefix}`,
      // initialRouteName: "NewOrder",
      screens: {
        "KooDoo Web Order": {
          // path: 'new-order/:outletId?/:tableId?/:tableCode?/:tablePax?/:waiterId?/:qrDateTimeEncrypted?',
          path: "/new-order/:tableId?/:waiterId?/:qrDateTimeEncrypted?",
        },

        // "Order Queue - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/order-queue",
        // },

        "KooDoo Web Order Generic": {
          path: "/new-order-generic/:tableId?",
        },
        "KooDoo Web Order Takeaway": {
          path: "/outlet/:subdomain?/takeaway",
        },
        "KooDoo Web Order Dine-In": {
          path: "/outlet/:subdomain?/dinein",
        },
        "KooDoo Web Order Delivery": {
          path: "/outlet/:subdomain?/delivery",
        },
        "KooDoo Web Order Scan": {
          path: "scan",
        },
        "KooDoo Web Order Error": {
          path: "error/:msg?",
        },
        "KooDoo Web Order Payment": {
          path: "payment/:subdomain?/:amount?/:appCode?/:channel?/:checksum?/:mpSecuredVerfified?/:msgType?/:orderId?/:pInstruction?/:statusCode?/:txnId?",
        },
        "KooDoo Web Order Payment Credit": {
          path: "payment-credit/:subdomain?/:amount?/:orderIdLocal?",
        },
        "Register - KooDoo Web Order": {
          path: "register/:orderId?/:qrDateTimeEncrypted?"
        },
        // "History - KooDoo Web Order": {
        //   path: "/history"
        // },
        "Register Outlet - KooDoo Web Order": {
          path: "/outlet/:subdomain?/register/:orderId?/:qrDateTimeEncrypted?",
        },
        "Reservation - KooDoo Web Order": {
          path: "/outlet/:subdomain?/reservation/:reservationId?",
        },
        "Reservation Details - KooDoo Web Order": {
          path: "/outlet/:subdomain?/reservation-info/:reservationId?",
        },
        "Reservation Summary - KooDoo Web Order": {
          path: "/outlet/:subdomain?/reservation-details/:reservationId?",
        },

        // "Order History Details - KooDoo Web Order": {
        //   //path: "/outlet/:subdomain?/order-details",
        //   path: "/outlet/:subdomain?/order-history",
        // },

        "Rewards - KooDoo Web Order": {
          path: "/rewards/:userIdHuman?"
        },

        "Claim Voucher - KooDoo Web Order": {
          path: "/outlet/:subdomain?/claim-voucher/:voucherId?",
        },
        // Old Queue
        // "Queue - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/queue"
        // },
        "Queue Confirmation - KooDoo Web Order": {
          path: "/outlet/:subdomain?/queue-confirmation"
        },

        // "Order Queue - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/queue-order",
        // },
        "Queue Now - KooDoo Web Order": {
          path: "/outlet/:subdomain?/queue",
        },
        "Queue Successful - KooDoo Web Order": {
          path: "/outlet/:subdomain?/queue-successful"
        },
      },
    },
    KooDoo: {
      path: `${prefix}/outlet/:subdomain?`,
      // initialRouteName: "Outlet - KooDoo Web Order",
      initialRouteName: "Outlet Menu - KooDoo Web Order",
      screens: {
        'Outlet - KooDoo Web Order': {
          path: '/',
        },

        "Order Queue - KooDoo Web Order": {
          path: "order-queue",
        },

        "Outlet Menu - KooDoo Web Order": {
          path: "menu",
        },
        "Product Details - KooDoo Web Order": {
          path: "menu/item",
        },
        "Cart - KooDoo Web Order": {
          path: "cart",
        },
        "Cart - KooDoo Web Order ": {
          path: "pay-cart",
        },
        "Voucher Cart - KooDoo Web Order": {
          path: "voucher-cart",
        },
        "Topup Credit Cart - KooDoo Web Order": {
          path: "credit-cart",
        },
        "NoVoucher Cart - KooDoo Web Order": {
          path: "novoucher-cart",
        },
        "Order History Details - KooDoo Web Order": {
          //path: "/outlet/:subdomain?/order-details",
          path: "order-history",
        },
        "Order History Details Takeaway - KooDoo Web Order": {
          //path: "/outlet/:subdomain?/order-details",
          path: "order-history-t",
        },

        "Order Receipt - KooDoo Web Order": {
          path: "receipt/:orderId?",
        },
        // "Order History Details - KooDoo Web Order": {
        //   //path: "/outlet/:subdomain?/order-details",
        //   path: "/outlet/order-details",
        // },
        // "Reservation - KooDoo Web Order": {
        //   path: "reservation",
        // },

        "Voucher Bundle - KooDoo Web Order": {
          path: "voucher-bundle",
        },


        "Upsell Menu - KooDoo Web Order": {
          path: "upsell-menu",
        },

        "Upsell Cart - KooDoo Web Order": {
          path: "upsell-cart",
        },

        "Upsell Reservation - KooDoo Web Order": {
          path: "upsell-reservation",
        },

        "Address - KooDoo Web Order": {
          path: "address",
        },
        "HomeAddress - KooDoo Web Order": {
          path: "homeaddress",
        },
        "EditAddress - KooDoo Web Order": {
          path: "editaddress",
        },
        "NewAddress - KooDoo Web Order": {
          path: "newaddress",
        },
        "WorkAddress - KooDoo Web Order": {
          path: "workaddress",
        },
        "AddAddress - KooDoo Web Order": {
          path: "addaddress",
        },
        "LoyaltyCampaignList - KooDoo Web Order": {
          path: "loyaltycampaignlist",
        },
        "LoyaltyCampaignDetails- KooDoo Web Order": {
          path: "loyaltycampaigndetails",
        },
        "Voucher List - KooDoo Web Order": {
          path: "voucher-list",
        },
        "Voucher Details - KooDoo Web Order": {
          path: "voucher-details",
        },
        "Buy Vouchers - KooDoo Web Order": {
          path: 'buy-vouchers'
        },

        "Notifications - KooDoo Web Order": {
          path: 'order-notification'
        },

        // "Reservation Details - KooDoo Web Order": {
        //   path: "/reservationdetails",
        // },
        // "Register - KooDoo Web Order": {
        //   path: "register/:orderId?/:qrDateTimeEncrypted?"
        // }

        "Register Outlet - KooDoo Web Order": {
          path: "/outlet/:subdomain?/register/:orderId?/:qrDateTimeEncrypted?",
        },
        // "Reservation Details - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/reservation-info/:reservationId?",
        // },
        // "Reservation Summary - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/reservation-details/:reservationId?",
        // },


        // "Register Outlet - KooDoo Web Order": {
        //   path: "/outlet/:subdomain?/register/:orderId?/:qrDateTimeEncrypted?",
        // },

        "Rewards - KooDoo Web Order": {
          path: "/rewards/:userIdHuman?"
        },

        "Outlet Rewards - KooDoo Web Order": {
          path: "outlet-rewards"
        },

        // "Claim Voucher - KooDoo Web Order":{
        //   path: "claim-voucher/:voucherId?",
        // },
        // "Vouchers List - KooDoo Web Order": {
        //   path: "/voucher-list",
        // },

        // "Vouchers History - KooDoo Web Order": {
        //   path: "/voucher-history",
        // },

        "Reward Details - KooDoo Web Order": {
          path: "outlet-reward-details",
        },

        "Stamp Details - KooDoo Rewards": {
          path: "outlet-stamp-details",
        },

        "Credit Details - KooDoo Web Order": {
          path: "outlet-credit-details",
        },

        "E-Invoice - KooDoo Web Order": {
          path: "einvoice/:orderUniqueId",
        },
      },
    },
    KooDooPreview: {
      path: `${prefix}/preview`,
      screens: {
        "Preview - KooDoo Web Order": {
          path: "/:previewId?",
        },
      },
    },
  },
};

const linking = {
  prefixes: [
    /* your linking prefixes */
  ],
  config: config,
};

const KooDooOrder = ({ navigation }) => {
  navigation.setOptions({ tabBarVisible: false });

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen
        name="KooDoo Web Order"
        component={NewOrderScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Generic"
        component={NewOrderGenericScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Takeaway"
        component={TakeawayScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Dine-In"
        component={DineInScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Delivery"
        component={DeliveryScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Payment"
        component={PaymentSuccessScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Payment Credit"
        component={PaymentCreditSuccessScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Scan"
        component={ScanScreen}
      />

      <Stack.Screen
        name="KooDoo Web Order Error"
        component={ErrorScreen}
      />

      <Stack.Screen
        name="Reservation - KooDoo Web Order"
        component={ReservationScreen}
      />

      <Stack.Screen
        name="Register - KooDoo Web Order"
        component={LoyaltySignUp}
      />

      <Stack.Screen
        name="Register Outlet - KooDoo Web Order"
        component={LoyaltySignUpOutlet}
      />

      <Stack.Screen
        name="Rewards - KooDoo Web Order"
        component={LoyaltyHistory}
      />

      <Stack.Screen
        name="Reservation Details - KooDoo Web Order"
        component={ReservationDetailsScreen}
      />

      <Stack.Screen
        name="Reservation Summary - KooDoo Web Order"
        component={ReservationSummary}
      />

      <Stack.Screen
        name="Claim Voucher - KooDoo Web Order"
        component={ClaimVoucher}
      />

      <Stack.Screen
        name="Queue - KooDoo Web Order"
        component={CreateQueueScreen}
      />

      <Stack.Screen
        name="Queue Confirmation - KooDoo Web Order"
        component={ConfirmQueueScreen}
      />

      <Stack.Screen
        name="Queue Now - KooDoo Web Order"
        component={QueueScreen}
      />

      <Stack.Screen
        name="Queue Successful - KooDoo Web Order"
        component={QueueSuccessfulScreen}
      />

    </Stack.Navigator>
  );
};

const KooDoo = ({ navigation }) => {
  navigation.setOptions({ tabBarVisible: false });

  return (
    <Stack.Navigator
      screenOptions={
        {
          // headerShown: false,
        }
      }
    >
      <Stack.Screen
        name="Outlet - KooDoo Web Order"
        component={OutletScreen}
      />

      <Stack.Screen
        name="Outlet Menu - KooDoo Web Order"
        component={OutletMenuScreen}
      />

      <Stack.Screen
        name="Product Details - KooDoo Web Order"
        component={MenuItemDetailsScreen}
      />

      <Stack.Screen
        name="Cart - KooDoo Web Order"
        component={CartScreen}
      />

      <Stack.Screen
        name="Cart - KooDoo Web Order "
        component={CartPayHybridScreen}
      />
      <Stack.Screen
        name="NoVoucher Cart - KooDoo Web Order"
        component={CartNoVoucherScreen}
      />

      <Stack.Screen
        name="Voucher Cart - KooDoo Web Order"
        component={CartTaggableVoucherScreen}
      />

      <Stack.Screen
        name="Topup Credit Cart - KooDoo Web Order"
        component={CartTopupCreditScreen}
      />

      <Stack.Screen
        name="Order History Details - KooDoo Web Order"
        component={OrderHistoryDetail}
      />

      <Stack.Screen
        name="Order History Details Takeaway - KooDoo Web Order"
        component={OrderHistoryDetailTakeaway}
      />

      {/* <Stack.Screen
        name="Order Receipt - KooDoo Web Order"
        component={OrderReceiptScreen}
      /> */}

      {/* <Stack.Screen
        name="Reservation - KooDoo Web Order"
        component={ReservationScreen}
      /> */}

      <Stack.Screen
        name="Address - KooDoo Web Order"
        component={AddressScreen}
      />

      <Stack.Screen
        name="HomeAddress - KooDoo Web Order"
        component={HomeAddress}
      />

      <Stack.Screen
        name="EditAddress - KooDoo Web Order"
        component={EditAddress}
      />

      <Stack.Screen
        name="NewAddress - KooDoo Web Order"
        component={NewAddress}
      />

      <Stack.Screen
        name="WorkAddress - KooDoo Web Order"
        component={WorkAddress}
      />

      <Stack.Screen
        name="AddAddress - KooDoo Web Order"
        component={AddAddress}
      />

      {/* <Stack.Screen
        name="LoyaltyCampaignList - KooDoo Web Order"
        component={LoyaltyCampaignListScreen}
      /> */}

      {/* <Stack.Screen
        name="LoyaltyCampaignDetails - KooDoo Web Order"
        component={LoyaltyCampaignDetailsScreen}
      /> */}

      <Stack.Screen
        name="Voucher List - KooDoo Web Order"
        component={TaggableVoucherListScreen}
      />

      <Stack.Screen
        name="Voucher Details - KooDoo Web Order"
        component={TaggableVoucherDetailsScreen}
      />

      <Stack.Screen
        name="Buy Vouchers - KooDoo Web Order"
        component={TaggableVoucherListPurchaseScreen}
      />

      <Stack.Screen
        name="Notifications - KooDoo Web Order"
        component={OrderNotificationListScreen}
      />

      <Stack.Screen
        name="Register - KooDoo Web Order"
        component={LoyaltySignUp}
      />

      <Stack.Screen
        name="Register Outlet - KooDoo Web Order"
        component={LoyaltySignUpOutlet}
      />

      <Stack.Screen
        name="Rewards - KooDoo Web Order"
        component={LoyaltyHistory}
      />

      <Stack.Screen
        name="Outlet Rewards - KooDoo Web Order"
        component={OutletRewardScreen}
      />

      <Stack.Screen
        name="Upsell Menu - KooDoo Web Order"
        component={UpsellingAfterCartScreen}
      />

      <Stack.Screen
        name="Upsell Cart - KooDoo Web Order"
        component={UpsellingAfterCheckoutScreen}
      />

      <Stack.Screen
        name="Upsell Reservation - KooDoo Web Order"
        component={ReservationUpsellingScreen}
      />

      {/* <Stack.Screen
        name="Reservation Details - KooDoo Web Order"
        component={ReservationDetailsScreen}
      /> */}

      {/* <Stack.Screen
        name="Vouchers List - KooDoo Web Order"
        component={VoucherScreen}
      />
      <Stack.Screen
        name="Vouchers History - KooDoo Web Order"
        component={VoucherHistory}
      /> */}

      <Stack.Screen
        name="Reward Details - KooDoo Web Order"
        component={RewardDetailsScreen}
      />

      <Stack.Screen
        name="Stamp Details - KooDoo Rewards"
        component={OR_StampDetails}
      />

      <Stack.Screen
        name="Credit Details - KooDoo Web Order"
        component={CreditDetailsScreen}
      />

      <Stack.Screen
        name="E-Invoice - KooDoo Web Order"
        component={EInvoiceScreen}
      />

      <Stack.Screen
        name="Order Queue - KooDoo Web Order"
        component={OrderQueueScreen}
      />

    </Stack.Navigator>
  );
};

const KooDooPreview = ({ navigation, route }) => {
  const { linkTo } = useLinkTo();
  const { previewId } = route.params || {};

  useEffect(() => {
    if (!previewId) {
      global.errorMsg = 'Invalid link.';
      linkTo && linkTo(`${prefix}/scan`);
    }
  }, [previewId, navigation]);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
      }}
    >
      <Stack.Screen
        name="Preview - KooDoo Web Order"
        component={OutletPreviewScreen}
      />
    </Stack.Navigator>
  );
};

// const onBackPress = () => {
//   return true;
// };

const AppNavigator = () => {
  return (
    <NavigationContainer
      // ref={navigationRef}
      linking={linking}
      fallback={<Text>Loading...</Text>}
    // onBackPressed={onBackPress}
    >
      <Tab.Navigator tabBar={() => null}>
        <Tab.Screen
          name="KooDooOrder"
          component={KooDooOrder}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        />

        <Tab.Screen
          name="KooDoo"
          component={KooDoo}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        />

        <Tab.Screen
          name="KooDooPreview"
          component={KooDooPreview}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  menuContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-evenly",
  },
  menuItemsCard: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
  circleContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    padding: 10,
  },
});

export default AppNavigator;
