import React, { useState, useEffect } from "react";
import { StyleSheet, View, Text, Dimensions, useWindowDimensions, Modal, TouchableOpacity, TextInput, Image, Animated, ScrollView, Platform, InteractionManager, Linking, } from "react-native";
// import firebase from 'firebase/app';
import { initializeApp } from "firebase/app";
import { getFirestore, initializeFirestore, enableIndexedDbPersistence, collection, query, where, getDocs, limit, setDoc, doc, orderBy, updateDoc } from "firebase/firestore";
import { getAnalytics } from "firebase/analytics";
import { getAuth, initializeAuth, browserLocalPersistence, indexedDBLocalPersistence, inMemoryPersistence, signInAnonymously } from "firebase/auth";
import { getStorage } from "firebase/storage";
import AppNavigator from "./navigation/AppNavigator";
// import loadable from '@loadable/component';
import { CommonStore } from "../src/store/commonStore";
import AwesomeAlert from 'react-native-awesome-alerts';
import AwesomeAlerteI from 'react-native-awesome-alerts';
import {
  listenToUserChanges,
  listenToLocationChanges, listenToSelectedOutletChanges, listenToSelectedOutletItemChanges, listenToCommonChanges, listenToSelectedOutletTagChanges, listenToSearchOutletTextChanges, listenToSearchOutletMerchantIdChanges, listenToSelectedOutletTableIdChanges, requestNotificationsPermission, getOutletById, isMobile, signInWithPhoneForCRMUser, listenToUserIdAnonymousChanges, listenToJoinedOrSplittedTableChanges, listenToSelectedOutletChangesCore, listenToUserIdAnonymousChangesStatic, isTestingOutlet, listenToUserAnonymousChanges
  , updateWebTokenAnonymous, mondayFirst
} from '../src/util/commonFuncs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Draggable from 'react-native-draggable';
import Colors from '../src/constant/Colors';
import { useNavigation } from '@react-navigation/native';
import { DataStore } from '../src/store/dataStore';
import { UserStore } from '../src/store/userStore';
// import messaging from '@react-native-firebase/messaging';
import { Collections } from '../src/constant/firebase';
import ApiClient from '../src/util/ApiClient';
import API from '../src/constant/API';
import { parseMessages } from '../src/util/notifications';
import { NotificationStore } from '../src/store/notificationStore';
import { APPLY_BEFORE, APPLY_DISCOUNT_PER, CONDITION_TYPE, PROMOTION_TYPE, PROMOTION_TYPE_VARIATION, STORE_TYPE } from '../src/constant/promotions';
import { prefix } from "./constant/env";
import Styles from "./constant/Styles";
import VoucherPromotionInfo from "./components/voucherPromotionInfo";
import GeneralAskUserInfo from "./components/generalAskUserInfo";
import { TempStore } from "./store/tempStore";
import { TableStore } from "./store/tableStore";
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesomeTTF from 'react-native-vector-icons/Fonts/FontAwesome.ttf';
import SimpleLineIconsTTF from 'react-native-vector-icons/Fonts/SimpleLineIcons.ttf';
import EntypoTTF from 'react-native-vector-icons/Fonts/Entypo.ttf';
import IoniconsTTF from 'react-native-vector-icons/Fonts/Ionicons.ttf';
import FeatherTTF from 'react-native-vector-icons/Fonts/Feather.ttf';
import AntDesignTTF from 'react-native-vector-icons/Fonts/AntDesign.ttf';
import MaterialCommunityIconsTTF from 'react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf';
import MaterialIconsTTF from 'react-native-vector-icons/Fonts/MaterialIcons.ttf';
import NunitoSansBoldTTF from './assets/fonts/NunitoSans-Bold.ttf';
import NunitoSansRegularTTF from './assets/fonts/NunitoSans-Regular.ttf';
import NunitoSansSemiBoldTTF from './assets/fonts/NunitoSans-SemiBold.ttf';
// const FontAwesomeTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/FontAwesome.ttf")));
// const SimpleLineIconsTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/SimpleLineIconsTTF.ttf")));
// const EntypoTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/EntypoTTF.ttf")));
// const IoniconsTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/IoniconsTTF.ttf")));
// const FeatherTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/FeatherTTF.ttf")));
// const AntDesignTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/AntDesignTTF.ttf")));
// const MaterialCommunityIconsTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/MaterialCommunityIconsTTF.ttf")));
// const NunitoSansBoldTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/NunitoSansBoldTTF.ttf")));
// const NunitoSansRegularTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/NunitoSansRegularTTF.ttf")));
// const NunitoSansSemiBoldTTF = loadable(() => lazyRetry(() => import("react-native-vector-icons/Fonts/NunitoSansSemiBoldTTF.ttf")));

// import loadable from '@loadable/component';
import moment from 'moment';
import imgLogo from "./asset/image/logo.png";
// import img1 from "./asset/image/img1.png";
// import img2 from "./asset/image/img2.png";
// import img3 from "./asset/image/img3.png";
import { ReactComponent as Foodturkey } from "./asset/svg/Food-turkey.svg"
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Entypo from "react-native-vector-icons/Entypo";
import { CHANNEL_TYPE, ORDER_TYPE, OUTLET_SHIFT_STATUS, WEEK } from "./constant/common";
import { PaymentStore } from "./store/paymentStore";
import AntDesign from "react-native-vector-icons/AntDesign";
// const moment = loadable.lib(() => import("moment"));
import axios from 'axios';

import { getMessaging, getToken, onMessage, isSupported } from "firebase/messaging";
// import { onBackgroundMessage } from "firebase/messaging/sw";
import { v4 as uuidv4 } from 'uuid';
import { LOYALTY_CAMPAIGN_TYPE } from "./constant/loyalty";
import WJsonStream from "w-json-stream";
import { idbSet, safelyExecuteIdb } from "./util/db";
import DropDownPicker from 'react-native-dropdown-picker';
import Checkbox from 'rc-checkbox';
import Toastify from 'toastify-js';
import "toastify-js/src/toastify.css";
import { saveAs } from 'file-saver';
import { CheckBox } from "react-native-web";
import ShowGreetingPopupModal from "./components/ShowGreetingPopupModal";

// Look at public/index.html!

moment.locale('en');

String.prototype.insert = function (index, string) {
  if (index > 0) {
    return this.substring(0, index) + string + this.substr(index);
  }

  return string + this;
};

const firebaseConfig = require('./config/firebase-web.json');

global.firebaseApp = initializeApp(firebaseConfig);

// Initialize Firestore with iOS/Safari-friendly network settings
try {
  const db = initializeFirestore(global.firebaseApp, {
    experimentalForceLongPolling: true,
    useFetchStreams: false,
  });

  // Attempt to enable IndexedDB persistence, but gracefully fall back on iOS Private Mode or unsupported cases
  enableIndexedDbPersistence(db).catch((e) => {
    console.warn('Firestore persistence disabled:', e && e.code ? e.code : e);
  });

  global.db = db;
} catch (e) {
  console.warn('initializeFirestore failed, falling back to getFirestore:', e);
  global.db = getFirestore(global.firebaseApp);
}

let messaging = null;
(async () => {
  const isMessagingSupported = await isSupported();
  if (isMessagingSupported) {
    messaging = getMessaging();

    onMessage(messaging, (payload) => {
      console.log('meessage received. ', payload);

      if (payload && payload.notification &&
        payload.notification.title &&
        payload.notification.body) {
        const notificationTitle = payload.notification.title;
        const notificationOptions = {
          body: payload.notification.body,
        };

        global.newsw.showNotification(notificationTitle, notificationOptions);
      }

      if (payload && payload.data &&
        payload.data.ti &&
        payload.data.bo) {
        const notificationTitle = payload.data.ti;
        const notificationOptions = {
          body: payload.data.bo,
        };

        global.newsw.showNotification(notificationTitle, notificationOptions);
      }
    });
  }
})();

// const messaging = getMessaging();

global.orderTypeTimerId = null;

global.crmUser = null;

global.visibleCategories = [];

global.toShowSignUpMemberModal = false;
global.checkOutletShiftAndOperatingHoursTimerTriggerTimes = 0;
global.checkOutletShiftAndOperatingHoursTimerId = null;

global.dso = false;
global.dsopn = false;
global.outletQrScanner = null;

global.notificationPermission = false;

global.analytics = getAnalytics(global.firebaseApp);

global.outletCustomTaxList = [];

// Configure Auth with robust persistence fallbacks for iOS Safari/Private Mode
try {
  global.auth = initializeAuth(global.firebaseApp, {
    persistence: [
      indexedDBLocalPersistence,
      browserLocalPersistence,
      inMemoryPersistence,
    ],
  });
} catch (e) {
  // If already initialized or environment does not allow initializeAuth, fall back
  console.warn('initializeAuth failed, falling back to getAuth:', e);
  global.auth = getAuth(global.firebaseApp);
}
global.storage = getStorage(global.firebaseApp);

global.userIdAnonymousLoaded = false;
global.userPhoneLoaded = false;

global.isClaimedVoucherShownOnce = false;

global.isRedeemVoucherModalShownOnce = false;

global.redeemVoucherTimeoutId = null;

global.userPhone = '';

global.takeawayOrders = [];

global.currPageStack = ['init'];

global.isTableJoinedOnTheFly = false;

global.linkToFunc = () => { };

global.isFromRecommendedItems = false;

global.currUpsellingCampaign = null;

global.selectedOutlet = null;

global.selectedOutletItemCategory = {};

global.subdomain = '';
global.subdomainOnly = '';

global.menuItemDetailModal = false; // for some reason, the hacks of back button acting weird issue (when back from new menu item details modal)

global.initCoreTimes = 0;

global.isUpdatedTablePax = false;

global.isUpdatedTablePax = false;

global.storedTableId = '';

global.cartItemsProcessed = [];

global.cartOutletItemAddOnChoiceDict = {};
global.outletsItemAddOnChoiceIdDict = {};

global.loyaltyStampBuyList = [];

global.intervalGR = null;

global.openingStartDateTime = Date.now();
global.openingEndDateTime = Date.now();

global.subscriberListenToSelectedOutletChangesCore = () => { };
global.subscriberListenToSelectedOutletChanges = () => { };
global.subscriberListenToSelectedOutletItemChanges = () => { };

global.isClaimVoucherButtonClicked = false;

// setInterval(() => {
//   CommonStore.update(s => {
//     s.timeCheckItem = Date.now();
//   });
// }, 30000);

setInterval(() => {
  CommonStore.update(s => {
    s.timestampPromotion = Date.now();

    s.timeCheckItem = Date.now();
  });
}, 60000);

const App = () => {
  const {
    width: windowWidth,
    height: windowHeight,
  } = useWindowDimensions();

  const [cartIcon, setCartIcon] = useState(false);

  const firebaseAuth = TempStore.useState(s => s.firebaseAuth);

  const alertObj = CommonStore.useState(s => s.alertObj);
  const alertObjeI = CommonStore.useState(s => s.alertObjeI);

  const isSwitchingOutlets = CommonStore.useState(s => s.isSwitchingOutlets);

  const userIdAnonymous = UserStore.useState(s => s.userIdAnonymous);

  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const userName = UserStore.useState(s => s.name);

  const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
  const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);

  const cartItems = CommonStore.useState(s => s.cartItems);
  const cartItemsProcessed = CommonStore.useState(s => s.cartItemsProcessed);

  const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);

  const linkToFunc = DataStore.useState(s => s.linkToFunc);

  const upsellingCampaignsAfterCart = DataStore.useState(s => s.upsellingCampaignsAfterCart);
  const upsellingCampaignsAfterCheckout = DataStore.useState(s => s.upsellingCampaignsAfterCheckout);
  const upsellingCampaignsAfterCheckoutRecommendation = DataStore.useState(s => s.upsellingCampaignsAfterCheckoutRecommendation);

  const currPage = CommonStore.useState(s => s.currPage);

  const currPageIframe = CommonStore.useState(s => s.currPageIframe);

  const userGroups = UserStore.useState(s => s.userGroups);
  const email = UserStore.useState(s => s.email);
  const userPhone = UserStore.useState(s => s.number);
  const outletId = CommonStore.useState(s => s.selectedOutlet);

  const anonymousPointsBalance = UserStore.useState(s => s.anonymousPointsBalance);
  const userPointsBalance = UserStore.useState(s => s.userPointsBalance);

  const selectedPromoCodePromotion = CommonStore.useState(s => s.selectedPromoCodePromotion);

  const selectedOutletPromotions = CommonStore.useState(s => s.selectedOutletPromotions);
  const availablePromotions = CommonStore.useState(s => s.availablePromotions);

  const selectedOutletPointsRedeemPackages = CommonStore.useState(s => s.selectedOutletPointsRedeemPackages);
  const availablePointsRedeemPackages = CommonStore.useState(s => s.availablePointsRedeemPackages);

  const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);
  const selectedOutletCRMSegmentsDict = CommonStore.useState(s => s.selectedOutletCRMSegmentsDict);
  const selectedOutletCRMUser = CommonStore.useState(s => s.selectedOutletCRMUser);

  const userLoyaltyStamps = CommonStore.useState(s => s.userLoyaltyStamps);
  const nUserLoyaltyCampaignInform = NotificationStore.useState(s => s.nUserLoyaltyCampaignInform);
  const selectedOutletLoyaltyStamps = CommonStore.useState(s => s.selectedOutletLoyaltyStamps);

  const toRedeemStackedLoyaltyStamp = CommonStore.useState(s => s.toRedeemStackedLoyaltyStamp);

  const selectedOutletLoyaltyCampaigns = CommonStore.useState(s => s.selectedOutletLoyaltyCampaigns);
  const selectedOutletUserLoyaltyCampaigns = CommonStore.useState(s => s.selectedOutletUserLoyaltyCampaigns);
  const availableLoyaltyCampaigns = CommonStore.useState(s => s.availableLoyaltyCampaigns);
  const selectedLoyaltyCampaign = CommonStore.useState(s => s.selectedLoyaltyCampaign);

  const orderType = CommonStore.useState(s => s.orderType);

  const navigationObj = DataStore.useState(s => s.navigationObj);
  const tickboxConsent = TempStore.useState(s => s.tickboxConsent);

  ////////////////////////////////////////////////////////////

  // 2022-08-04 - Vouchers support

  const taggableVouchers = CommonStore.useState(s => s.selectedOutletTaggableVouchersAll);
  const claimVoucherList = TempStore.useState(s => s.claimVoucherList);
  const userTaggableVouchers = CommonStore.useState(s => s.userTaggableVouchers);

  const availableUserTaggableVouchers = CommonStore.useState(s => s.availableUserTaggableVouchers);

  ///////////////////////////////////////////////////////////////////

  const typedVoucherCode = TempStore.useState(s => s.typedVoucherCode);

  ///////////////////////////////////////////////////////////////////

  const showUserHelpPopup = TempStore.useState(s => s.showUserHelpPopup);

  ///////////////////////////////////////////////////////////////////

  const cachedAddOnChoiceIdDict = CommonStore.useState(s => s.cachedAddOnChoiceIdDict);

  ///////////////////////////////////////////////////////////////////

  // 2024-08-08 - to resolve the random isue of, currOutletShiftStatus and outletsOpeningDict not being retrieved via firestore snapshot correctly, but the outletItems got retrieved

  const currOutletShiftStatus = CommonStore.useState(s => s.currOutletShiftStatus);
  const outletsOpeningDict = CommonStore.useState(s => s.outletsOpeningDict);

  ///////////////////////////////////////////////////////////////////

  const toWaitForQrLoading = TempStore.useState(
    (s) => s.toWaitForQrLoading
  );
  const qrGenericOutlet = TempStore.useState(
    (s) => s.qrGenericOutlet
  );
  const qrGenericJson = TempStore.useState(
    (s) => s.qrGenericJson
  );
  const qrGenericParams = TempStore.useState(
    (s) => s.qrGenericParams
  );

  const newsw = TempStore.useState(
    (s) => s.newsw
  );

  const showStartAsGuestButton = TempStore.useState(
    (s) => s.showStartAsGuestButton
  );

  const cartItemsPayment = PaymentStore.useState(s => s.cartItemsPayment);
  const outletIdPayment = PaymentStore.useState(s => s.outletIdPayment);
  const dateTimePayment = PaymentStore.useState(s => s.dateTimePayment);
  const orderTypePayment = PaymentStore.useState(s => s.orderTypePayment);
  const timestampPayment = PaymentStore.useState(s => s.timestampPayment);

  const selectedCustomerPointsTransactionsEmail = UserStore.useState((s) => s.selectedCustomerPointsTransactionsEmail);
  const selectedCustomerPointsBalanceEmail = UserStore.useState((s) => s.selectedCustomerPointsBalanceEmail);
  const selectedCustomerPointsTransactionsPhone = UserStore.useState((s) => s.selectedCustomerPointsTransactionsPhone);
  const selectedCustomerPointsBalancePhone = UserStore.useState((s) => s.selectedCustomerPointsBalancePhone);

  {/* 20240618 e-invoice */ }
  /////////////////////////////////////////////////////////////////////////////////
  const showSignUpMember = TempStore.useState((s) => s.showSignUpMember);

  const [epStateToTemp, setEpStateToTemp] = useState('sgr');
  const [epNameToTemp, setEpNameToTemp] = useState('');
  const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
  const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
  const [epCityToTemp, setEpCityToTemp] = useState('');
  const [epCodeToTemp, setEpCodeToTemp] = useState(''); // postcode
  const [epIdTypeToTemp, setEpIdTypeToTemp] = useState('NRIC');
  const [epIdToTemp, setEpIdToTemp] = useState('');
  const [epTinToTemp, setEpTinToTemp] = useState('');
  const [epEmailToTemp, setEpEmailToTemp] = useState('');

  const epStateTo = UserStore.useState((s) => s.epStateTo);
  const epNameTo = UserStore.useState((s) => s.epNameTo);
  const epPhoneTo = UserStore.useState((s) => s.epPhoneTo);
  const epAddr1To = UserStore.useState((s) => s.epAddr1To);
  const epCityTo = UserStore.useState((s) => s.epCityTo);
  const epCodeTo = UserStore.useState((s) => s.epCodeTo);
  const epIdTypeTo = UserStore.useState((s) => s.epIdTypeTo);
  const epIdTo = UserStore.useState((s) => s.epIdTo);
  const epTinTo = UserStore.useState((s) => s.epTinTo);
  const epEmailTo = UserStore.useState((s) => s.epEmailTo);

  const [eInvoiceInfo, setEInvoiceInfo] = useState(false);
  const [openProvince, setOpenProvince] = useState(false);
  const [openIdType, setOpenIdType] = useState(false);
  const [rating, setRating] = useState(5);
  const [review, setReview] = useState('A very good enjoyable experience.');
  const [birthday, setBirthday] = useState(moment(Date.now()));
  const gReview = UserStore.useState((s) => s.gReview);
  const showVoucherInfo = TempStore.useState(s => s.showVoucherInfo);
  const showVoucherPromotionInterestedInfo = TempStore.useState(s => s.showVoucherPromotionInterestedInfo);
  const [submittedGoogleReview, setSubmittedGoogleReview] = useState(false);
  const [address, setAddress] = useState('');
  const [lat, setLat] = useState(0);
  const [lng, setLng] = useState(0);
  const [isVerified, setIsVerified] = useState(false); // herks test

  ////////////////////////////////////////////////////////////////

  const idtypeOption = [
    {
      label: 'IC',
      value: 'NRIC',
    },
    {
      label: 'Passport',
      value: 'PASSPORT',
    },
    {
      label: 'MyTentera',
      value: 'ARMY',
    },
  ];

  const provinceOption = [
    {
      label: 'Johor',
      value: 'jhr',
    },
    {
      label: 'Kedah',
      value: 'kdh',
    },
    {
      label: 'Kelantan',
      value: 'ktn',
    },
    {
      label: 'Melaka',
      value: 'mlk',
    },
    {
      label: 'Negeri Sembilan',
      value: 'nsn',
    },
    {
      label: 'Pahang',
      value: 'phg',
    },
    {
      label: 'Perak',
      value: 'prk',
    },
    {
      label: 'Perlis',
      value: 'pls',
    },
    {
      label: 'Pulau Pinang',
      value: 'png',
    },
    {
      label: 'Selangor',
      value: 'sgr',
    },
    {
      label: 'Terengganu',
      value: 'trg',
    },
    {
      label: 'Kuala Lumpur',
      value: 'kul',
    },
    {
      label: 'Putra Jaya',
      value: 'pjy',
    },
    {
      label: 'Sarawak',
      value: 'srw',
    },
    {
      label: 'Sabah',
      value: 'sbh',
    },
    {
      label: 'Labuan',
      value: 'lbn',
    },
  ];

  useEffect(() => {
    // if (!('PushManager' in window)) {
    //   console.warn('Push notifications are not supported on this browser/platform.');

    //   Toastify({
    //     text: `Push notifications are not supported on this browser/platform.`,
    //     duration: 3000,
    //     // destination: "https://github.com/apvarun/toastify-js",
    //     newWindow: true,
    //     close: false,
    //     gravity: "top", // `top` or `bottom`
    //     position: "right", // `left`, `center` or `right`
    //     stopOnFocus: true, // Prevents dismissing of toast on hover
    //     style: {
    //       background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
    //       color: 'white',

    //       // marginLeft: '15px !important',
    //       // marginRight: '15px !important',
    //     },
    //     onClick: function () { } // Callback after click
    //   }).showToast();

    //   return;
    // }

    if (!('serviceWorker' in navigator)) {
      console.warn('Service workers are not supported on this browser/platform.');

      Toastify({
        text: `Service workers are not supported on this browser/platform.`,
        duration: 3000,
        // destination: "https://github.com/apvarun/toastify-js",
        newWindow: true,
        close: false,
        gravity: "top", // `top` or `bottom`
        position: "right", // `left`, `center` or `right`
        stopOnFocus: true, // Prevents dismissing of toast on hover
        style: {
          background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
          color: 'white',

          // marginLeft: '15px !important',
          // marginRight: '15px !important',
        },
        onClick: function () { } // Callback after click
      }).showToast();

      return;
    }
  }, []);

  // 2024-06-19 - pop state handlers moved to here
  useEffect(() => {
    const popStateListener = async e => {
      // e.preventDefault();
      // console.log('unload!');

      // linkTo(`${prefix}/outlet/menu`);

      // window.history.pushState(null, '', window.location.href);

      if (
        global.menuItemDetailModal
        // false
      ) {
        CommonStore.update(s => {
          s.menuItemDetailModal = false;
        });

        global.menuItemDetailModal = false;

        // if (!subdomain) {
        //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
        // } else {
        //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
        // }        

        // window.history.pushState({
        //   page: 'menuItemDetailModal',
        // }, '');

        // window.history.pushState({
        //   page: 'menuItemDetailModal',
        // }, '');

        // window.history.go(1);

        // const subdomain = await AsyncStorage.getItem("latestSubdomain");

        // if (!subdomain) {
        //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
        // } else {
        //   global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
        // }

        // e.preventDefault();
      }
      else {
        var isNeededToPopState = true;

        if (global.currPageStack[global.currPageStack.length - 1] === 'RewardDetailsScreen') {
          isNeededToPopState = false;
        }

        if (global.currPageStack[global.currPageStack.length - 1] === 'OR_StampDetails') {
          isNeededToPopState = false;
        }

        if (global.currPageStack[global.currPageStack.length - 1] === 'TaggableVoucherListScreen') {
          isNeededToPopState = false;
        }

        // if (global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetail') {
        //   isNeededToPopState = false;
        // }

        if (
          global.currPageStack[global.currPageStack.length - 1] === 'CartScreen'
          &&
          global.currPageStack[global.currPageStack.length - 2] === 'init'
        ) {
          if (global.clickedCartIcon) {
            isNeededToPopState = false;

            const subdomain = await AsyncStorage.getItem("latestSubdomain");

            if (!subdomain) {
              global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`);
            }
            else {
              global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);
            }
          }
        }

        if (
          global.currPageStack[global.currPageStack.length - 1] === 'CartScreen'
          &&
          (
            global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetail'
            ||
            global.currPageStack[global.currPageStack.length - 2] === 'OrderHistoryDetailTakeaway'
          )
        ) {
          isNeededToPopState = false;
        }

        if (global.currPageStack[global.currPageStack.length - 1] === 'RerservationDetails' ||
          global.currPageStack[global.currPageStack.length - 1] === 'Rerservation'
        ) {
          isNeededToPopState = false;
        }

        if (isNeededToPopState) {
          const subdomain = await AsyncStorage.getItem("latestSubdomain");

          const onUpdatingCartItem = await AsyncStorage.getItem('onUpdatingCartItem');
          if (onUpdatingCartItem !== '1') {
            ///////////////////////////////////

            // 2024-06-19 - for OutletScreen support

            if (
              global.currPageStack[global.currPageStack.length - 1] === 'OutletScreen'
            ) {
              // global.currPageStack.pop();

              // TempStore.update(s => {
              //   s.showGreetingPopup = false;
              // });

              const subdomain = await AsyncStorage.getItem("latestSubdomain");

              if (subdomain) {
                global.currPageStack = [];

                TempStore.update(s => {
                  s.showGreetingPopup = false;
                });

                // linkTo && linkTo(`${prefix}/outlet/${selectedOutlet.subdomain}`);

                window.location.replace(`${prefix}/outlet/${subdomain}`);
              }
            }
            else {
              if (!subdomain) {
                global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);

                // if (!global.isInitBack) {
                //   global.isInitBack = true;

                //   window.history.back();
                // }
              } else {
                global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);

                // if (!global.isInitBack) {
                //   global.isInitBack = true;

                //   window.history.back();
                // }
              }
            }
          }
        }
      }
    };

    // window.addEventListener(
    //   "popstate",
    //   popStateListener,
    //   false
    // );

    window.onpopstate = popStateListener;
  }, []);

  useEffect(() => {
    CommonStore.update((s) => {
      s.alertObjeI = null;
    })
  }, [])

  //////////////////////////////////

  // 2024-12-05 - fixed the possible issue of cart icon not showing

  useEffect(() => {
    if (
      cartIcon
      &&
      !(
        currPage !== 'Cart' &&
        currPage !== 'UpsellMenu' &&
        currPage !== 'UpsellCart' &&
        currPage !== 'Reservation' &&
        currPage !== 'ReservationDetails' &&
        currPage !== 'ReservationSummary'
      )
    ) {
      // means the currPage show that is one of the pages listed above

      // let's check the url first

      setTimeout(() => {
        if (window.location.href.includes('/menu')) {
          // means now should be in menu page, but currPage not updated correctly

          global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);

          CommonStore.update((s) => {
            s.currPage = '';
            s.currPageStack = [];
          });
        }
      }, 500);
    }
  }, [
    cartIcon,
    currPage,
  ]);

  //////////////////////////////////

  {/* 20240618 e-invoice */ }
  useEffect(() => {
    if (userName && userPhone) {
      setIsVerified(true);
    }
  }, [userName, userPhone]);

  useEffect(() => {
    if (epStateTo) {
      setEpStateToTemp(epStateTo);
    }

    if (epNameTo) {
      setEpNameToTemp(epNameTo);
    }
    else if (userName) {
      setEpNameToTemp(userName);
    }

    if (epPhoneTo) {
      setEpPhoneToTemp(epPhoneTo);
    }
    else if (userPhone) {
      setEpPhoneToTemp(userPhone);
    }

    if (epAddr1To) {
      setEpAddr1ToTemp(epAddr1To);
    }

    if (epCityTo) {
      setEpCityToTemp(epCityTo);
    }

    if (epCodeTo) {
      setEpCodeToTemp(epCodeTo);
    }

    if (epIdTypeTo) {
      setEpIdTypeToTemp(epIdTypeTo);
    }

    if (epIdTo) {
      setEpIdToTemp(epIdTo);
    }

    if (epTinTo) {
      setEpTinToTemp(epTinTo);
    }

    if (epEmailTo) {
      setEpEmailToTemp(epEmailTo);
    }
  }, [
    epStateTo,
    epNameTo,
    epPhoneTo,
    epAddr1To,
    epCityTo,
    epCodeTo,
    epIdTypeTo,
    epIdTo,
    epTinTo,
    epEmailTo,

    userName,
    userPhone,
  ]);

  useEffect(() => {
    document.body.style.webkitTouchCallout = 'none';

    // signInAnonymously(global.auth)
    //   .then((result) => {
    //   });

    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2 seconds

    const attemptSignIn = async (retryCount = 0) => {
      try {
        const result = await signInAnonymously(global.auth);
        console.log('Anonymous auth successful', result);

        TempStore.update(s => {
          s.firebaseAuth = true;
        });

        return result;
      } catch (error) {
        console.error('Auth error:', error.code, error.message);

        if (error.code === 'auth/network-request-failed' && retryCount < MAX_RETRIES) {
          console.log(`Retrying sign in... Attempt ${retryCount + 1}/${MAX_RETRIES}`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          return attemptSignIn(retryCount + 1);
        }

        throw error;
      }
    };

    setTimeout(() => {
      attemptSignIn().catch(error => {
        console.error('Final auth error:', error);
        // Handle final error - maybe show a user-friendly message
      });
    }, 0);
  }, []);

  useEffect(() => {
    const handleMessage = (event) => {
      try {
        if (event && event.data && !event.data.startsWith('setImmediate')) {
          const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
          if (!data || typeof data !== 'object') return;

          if (data.toggleMobile) {
            global.toggleMobile = true;
            // CommonStore.update(s => { s.isLoading = false });
          }
        }
      }
      catch (err) {
        console.error('Invalid message:', err);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    const message = {
      pageName: currPageIframe,
      selectedOutletItemId: (selectedOutletItem.uniqueId) ? selectedOutletItem.uniqueId : '',
      ucAfterCartIdList: upsellingCampaignsAfterCart.map(campaign => campaign.uniqueId),
      ucAfterCheckoutIdList: upsellingCampaignsAfterCheckout.map(campaign => campaign.uniqueId),
      ucAfterCheckoutRecommendationIdList: upsellingCampaignsAfterCheckoutRecommendation.map(campaign => campaign.uniqueId),
    };

    const messageString = JSON.stringify(message);

    window.parent.postMessage(messageString, '*');
    console.log('message transfer', messageString);
  }, [
    currPageIframe,
    selectedOutletItem,

    upsellingCampaignsAfterCart,
    upsellingCampaignsAfterCheckout,
    upsellingCampaignsAfterCheckoutRecommendation,
  ]);

  useEffect(() => {
    setTimeout(() => {
      axios.get("https://worldtimeapi.org/api/timezone/Asia/Kuala_Lumpur")
        .then(response => {
          // Handle the response data
          // const resultDiv = document.getElementById("result");
          // resultDiv.innerHTML = JSON.stringify(response.data, null, 2);
          console.log(response);

          if (response && response.data && response.data.datetime) {
            console.log('test date/time');
            console.log(moment().format('YYYY MM DD, HH:mm A'));

            const offset = new Date(response.data.datetime).getTime() - Date.now();
            moment.now = function () {
              return offset + Date.now();
            };

            console.log('test date/time');
            console.log(moment().format('YYYY MM DD, HH:mm A'));

            // Date.now = function() {
            //   // Add your custom logic here
            //   return offset + currDateTime; // Adding 1 second as an example
            //   return modifiedTimestamp;
            // };
          }
        })
        .catch(error => {
          // Handle any errors that occur during the request
          console.error("Error:", error);
        });
    }, 0);

    // setTimeout(async () => {
    //   var storedUserName = await AsyncStorage.getItem('storedUserName');
    //   var storedUserPhone = await AsyncStorage.getItem('storedUserPhone');

    //   if (storedUserName && storedUserPhone) {
    //     // no need show start as guest button

    //     // TempStore.update(s => {
    //     //   s.showStartAsGuestButton = false;
    //     // });
    //   }
    // }, 0);
  }, []);


  ///////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (cartItemsPayment.length > 0 && outletIdPayment && dateTimePayment &&
      orderTypePayment && selectedOutlet && selectedOutlet.uniqueId &&
      orderType) {
      if (orderTypePayment === orderType &&
        outletIdPayment === selectedOutlet.uniqueId &&
        moment().diff(dateTimePayment, 'hour') <= 6) {
        CommonStore.update(s => {
          s.cartItems = cartItemsPayment;
        });
      }
    }
  }, [cartItemsPayment, outletIdPayment, dateTimePayment, orderTypePayment, timestampPayment,
    selectedOutlet, orderType]);

  ///////////////////////////////////////////////////////////////////

  // 2025-05-05 - check order type

  useEffect(() => {
    if (global.orderTypeTimerId) {
      clearTimeout(global.orderTypeTimerId);
    }

    global.orderTypeTimerId = setTimeout(() => {
      if (orderType === ORDER_TYPE.DELIVERY) {
        global.errorMessage = 'Please visit the page using the outlet QR. (SA-0102)';

        global.linkToFunc && global.linkToFunc(`${prefix}/error`);
      }

      if (orderType === ORDER_TYPE.INVALID) {
        global.errorMessage = 'Please visit the page using the outlet QR. (SA-0103)';

        global.linkToFunc && global.linkToFunc(`${prefix}/error`);
      }
    }, 10000);
  }, [orderType]);

  ///////////////////////////////////////////////////////////////////

  const outletsItemAddOnChoiceIdDict = CommonStore.useState(
    (s) => s.outletsItemAddOnChoiceIdDict
  );

  useEffect(() => {
    let cachedAddOnChoiceIdDictTemp = {
      ...cachedAddOnChoiceIdDict,

      ...outletsItemAddOnChoiceIdDict,
    };

    CommonStore.update(s => {
      s.cachedAddOnChoiceIdDict = cachedAddOnChoiceIdDictTemp;
    });
  }, [outletsItemAddOnChoiceIdDict]);

  ///////////////////////////////////////////////////////////////////

  // const selectedOutletTablePax = CommonStore.useState(s => s.selectedOutletTablePax);

  ///////////////////////////////////////////////////////////////////

  // 2022-10-07 - Auto login the user if stored name and phone number

  useEffect(() => {
    (async () => {
      // console.log('selectedOutlet');
      // console.log(selectedOutlet);

      // if (selectedOutlet === null) {
      //   global.linkToFunc && global.linkToFunc(`${prefix}/error`);
      // }

      await signInWithPhoneForCRMUser(selectedOutlet);
    })();
  }, [selectedOutlet]);

  ///////////////////////////////////////////////////////////////////

  const loadAsyncStorage = async () => {
    // 2022-10-08 - No need

    // const cartItemsRaw = await AsyncStorage.getItem(`${firebaseUid}.cartItems`);
    // const cartOutletId = await AsyncStorage.getItem(`${firebaseUid}.cartOutletId`);

    // if (cartItemsRaw) {
    //   DataStore.update(s => {
    //     s.cartItems = [
    //       // ...s.cartItems,
    //       ...JSON.parse(cartItemsRaw),
    //     ];
    //   });
    // }

    // if (cartOutletId) {
    //   CommonStore.update(s => {
    //     s.cartOutletId = cartOutletId;
    //   });
    // }
  };

  // const updateTablePaxDirectly = async (json, outlet, firebaseUid) => {
  //   const body = {
  //     tableId: json.tableId,
  //     // pax: seatingPax,
  //     pax: 1,
  //     // outletId: currOutlet.uniqueId,
  //     outletId: json.outletId,
  //   };

  //   const subdomain = global.subdomainOnly;

  //   if (!subdomain) {
  //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
  //   }
  //   else {
  //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
  //   }

  //   ApiClient.POST(API.updateOutletTablePaxByUser, body).then(async (result) => {
  //     if (result && result.status === 'success') {
  //       console.log('ok');

  //       global.isUpdatedTablePax = true;

  //       // await AsyncStorage.setItem('latestOutletId', currOutlet.uniqueId);

  //       CommonStore.update(s => {
  //         // 2022-10-08 - Reset cart items
  //         s.cartItems = [];
  //         s.cartItemsProcessed = [];
  //         s.cartOutletId = outlet.uniqueId;

  //         s.selectedOutletItem = {};
  //         s.selectedOutletItemAddOn = {};
  //         s.selectedOutletItemAddOnChoice = {};
  //         s.onUpdatingCartItem = null;

  //         s.isLoading = false;

  //         s.molpayResult = null;

  //         s.payHybridBody = null;
  //         s.paymentDetails = null;
  //         s.orderIdCreated = '';
  //       });

  //       CommonStore.update(s => {
  //         // s.selectedOutlet = qrGenericOutlet;

  //         s.scannedQrData = json;

  //         s.orderType = ORDER_TYPE.DINEIN;

  //         s.selectedOutletTableId = json.tableId;
  //         s.selectedOutletWaiterId = json.waiterId;
  //         s.selectedOutletTablePax = json.tablePax;
  //         s.selectedOutletTableCode = json.tableCode;

  //         s.timestampOutletCategory = Date.now();
  //       });
  //     }
  //   });
  // };

  useEffect(() => {
    setTimeout(() => {
      CommonStore.subscribe(
        (s) => s,
        async (commonStore) => {
          // if (commonStore.selectedOutlet && commonStore.selectedOutlet.uniqueId) {
          //   await AsyncStorage.setItem('@commonStore', JSON.stringify(commonStore));
          // }

          // await AsyncStorage.setItem('@commonStore', JSON.stringify(commonStore));

          await WJsonStream.stringify(commonStore).then(async res => {
            safelyExecuteIdb(async () => {
              await idbSet('@commonStore', res);
            });
          });
        },
      );

      DataStore.subscribe(
        (s) => s,
        async (dataStore) => {
          // if (dataStore.linkToFunc) {
          //   await AsyncStorage.setItem('@dataStore', JSON.stringify(dataStore));
          // }        

          // await AsyncStorage.setItem('@dataStore', JSON.stringify(dataStore));

          await WJsonStream.stringify(dataStore).then(async res => {
            safelyExecuteIdb(async () => {
              await idbSet('@dataStore', res);
            });
          });
        },
      );

      UserStore.subscribe(
        (s) => s,
        async (userStore) => {
          // if (userStore.userIdAnonymous) {
          //   await AsyncStorage.setItem('@userStore', JSON.stringify(userStore));
          // }        

          // await AsyncStorage.setItem('@userStore', JSON.stringify(userStore));

          await WJsonStream.stringify(userStore).then(async res => {
            safelyExecuteIdb(async () => {
              await idbSet('@userStore', res);
            });
          });
        },
      );

      TableStore.subscribe(
        (s) => s,
        async (tableStore) => {
          // if (tableStore && tableStore.currPendingOrder && tableStore.currPendingOrder.uniqueId) {
          //   await AsyncStorage.setItem('@tableStore', JSON.stringify(tableStore));
          // }        

          // await AsyncStorage.setItem('@tableStore', JSON.stringify(tableStore));

          await WJsonStream.stringify(tableStore).then(async res => {
            safelyExecuteIdb(async () => {
              await idbSet('@tableStore', res);
            });
          });
        },
      );

      PaymentStore.subscribe(
        (s) => s,
        async (paymentStore) => {
          // if (paymentStore && paymentStore.outletIdPayment) {
          //   await AsyncStorage.setItem('@paymentStore', JSON.stringify(paymentStore));
          // }        

          // await AsyncStorage.setItem('@paymentStore', JSON.stringify(paymentStore));

          await WJsonStream.stringify(paymentStore).then(async res => {
            safelyExecuteIdb(async () => {
              await idbSet('@paymentStore', res);
            });
          });
        },
      );
    }, 3000);

    console.log('isMobile');
    console.log(isMobile());
  }, []);

  useEffect(() => {
    if (firebaseUid !== '' && outletId && outletId.uniqueId && firebaseAuth) {

      typeof global.subscriberListenToUserChanges === 'function' && global.subscriberListenToUserChanges();
      global.subscriberListenToUserChanges = () => { };

      let subscriber = listenToUserChanges(firebaseUid, email, userPhone, outletId);

      global.subscriberListenToUserChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
      // listenToUserChanges(firebaseUid, email, userPhone);

      // loadAsyncStorage();
    }
  }, [firebaseUid, email, userPhone, outletId, firebaseAuth]);

  const registerSW = async () => {
    if ('serviceWorker' in navigator) {
      // const registration = await navigator.serviceWorker.register(`${process.env.PUBLIC_URL}/sw.js`);
      // return registration;
      console.log(`${process.env.PUBLIC_URL}/sw.js`);

      navigator.serviceWorker
        .register(`${process.env.PUBLIC_URL}/sw.js`)
        .then(function (registration) {
          // console.log('Service Worker registered with scope:', registration.scope);
          // global.newsw = registration;
          console.log(registration);

          registration.pushManager.getSubscription()
            .then(pushSubscription => {
              if (!pushSubscription) {
                //the user was never subscribed
                // subscribeSW(registration);

                console.log('never subscribe before!');
              }
              else {
                //check if user was subscribed with a different key
                let json = pushSubscription.toJSON();
                let public_key = json.keys.p256dh;

                console.log(public_key);

                if (public_key !== 'BNbBBLIJ1JuSSQJJDelk0cdhXfC-Xr6Qr9VRODG1kUSO4re5oHYM5TJajl2zidaAAqUUOlsvXeVquxrOSeROVyk') {
                  pushSubscription.unsubscribe().then(successful => {
                    // You've successfully unsubscribed
                    // subscribeSW(registration);
                    console.log('unsubscribed!');
                  }).catch(e => {
                    // Unsubscription failed
                    console.error(e);
                  })
                }
              }
            });

          console.log('success!');
        })
        .catch(function (error) {
          console.error(error);
        });
    }
  }

  const requestNotificationPermission = async () => {
    try {
      const permission = await Notification.requestPermission();
      console.log('Notification permission granted');

      if (permission === 'granted') {
        global.notificationPermission = true;
      } else if (permission === 'denied') {
        Toastify({
          text: `Push notifications permission denied.`,
          duration: 3000,
          // destination: "https://github.com/apvarun/toastify-js",
          newWindow: true,
          close: false,
          gravity: "top", // `top` or `bottom`
          position: "right", // `left`, `center` or `right`
          stopOnFocus: true, // Prevents dismissing of toast on hover
          style: {
            background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
            color: 'white',

            // marginLeft: '15px !important',
            // marginRight: '15px !important',
          },
          onClick: function () { } // Callback after click
        }).showToast();
      } else {
        Toastify({
          text: `Push notifications permission dismissed.`,
          duration: 3000,
          // destination: "https://github.com/apvarun/toastify-js",
          newWindow: true,
          close: false,
          gravity: "top", // `top` or `bottom`
          position: "right", // `left`, `center` or `right`
          stopOnFocus: true, // Prevents dismissing of toast on hover
          style: {
            background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
            color: 'white',

            // marginLeft: '15px !important',
            // marginRight: '15px !important',
          },
          onClick: function () { } // Callback after click
        }).showToast();
      }
    } catch (error) {
      console.error('messaging error requesting notification permission:', error);
    }
  };

  const getFCMToken = async () => {
    getToken(messaging, {
      serviceWorkerRegistration: global.newsw,
      vapidKey: prefix === '/web'
        ?
        'BF-idh6XuuaEb9fboFE-yB1o7nweMugweTQo_ZyfnQK7GPAKGeds1eeoGJxL9VAm5Po6P6R7Qun1ZvqtaVsekiQ'
        :
        'BHqp6SiRwg5PTOujZAb4whiOTb0bg1K-ue_8fpZv-FRix02xV9WmLcMPYjZwxjqLX5jLPbid6ZpEsbhRzKzhSus'
      ,
    })
      .then(async (currentToken) => {
        if (currentToken) {
          console.log("currentToken", currentToken);

          // 2023-10-06 - Wait for global.userPhone value to be available
          setTimeout(async () => {
            const webTokenAnonymousSnapshot = await getDocs(
              query(
                collection(global.db, Collections.WebTokenAnonymous),
                where('uniqueId', '==', userIdAnonymous),
                limit(1),
              )
            );

            let webTokenAnonymous = null;
            if (!webTokenAnonymousSnapshot.empty) {
              webTokenAnonymous = webTokenAnonymousSnapshot.docs[0].data();

              // if (
              //   webTokenAnonymous.FCMToken === currentToken
              //   ||
              //   (
              //     (webTokenAnonymous.userPhone === undefined || webTokenAnonymous.userPhone === '')
              //     &&
              //     userPhone
              //   )
              // ) {
              //   return;
              // }

              console.log(webTokenAnonymous.FCMToken);
              console.log(currentToken);
              console.log(webTokenAnonymous.userPhone);
              console.log(global.userPhone)

              global.userIdAnonymous = userIdAnonymous;

              if (
                true
                // webTokenAnonymous.FCMToken !== currentToken
                // ||
                // webTokenAnonymous.userPhone !== global.userPhone
              ) {
                console.log('update the doc');
                console.log(userIdAnonymous);
                console.log(global.swSubscription);

                await setDoc(
                  doc(
                    global.db, Collections.WebTokenAnonymous, userIdAnonymous,
                  ),
                  {
                    FCMToken: currentToken,

                    userPhone: global.userPhone,

                    swSub: typeof global.swSubscription === 'string' ? global.swSubscription : null,

                    userIdAnonymous: userIdAnonymous,

                    updatedAt: Date.now(),
                  },
                  {
                    merge: true,
                  }
                );
              }

              console.log("currentToken doc updated in WebTokenAnonymous", userIdAnonymous);
            }
            else {
              console.log('create the doc');
              console.log(userIdAnonymous);
              console.log(global.swSubscription);

              await setDoc(
                doc(
                  global.db, Collections.WebTokenAnonymous, userIdAnonymous,
                ),
                {
                  uniqueId: userIdAnonymous,
                  FCMToken: currentToken,

                  userPhone: global.userPhone,

                  swSub: typeof global.swSubscription === 'string' ? global.swSubscription : null,

                  userIdAnonymous: userIdAnonymous,

                  createdAt: Date.now(),
                  updatedAt: Date.now(),
                  deletedAt: null,
                },
                {
                  merge: true,
                }
              );
              console.log("new currentToken doc created in WebTokenAnonymous", userIdAnonymous);
            }
          }, 5000);
        } else {
          console.log('No registration token available. Request permission to generate one.');
        }
      }).catch((err) => {
        console.log('An error occurred while retrieving token. ', err);
      });
  };

  useEffect(() => {
    (async () => {
      if (!newsw || userIdAnonymous === "none") {
        console.log(newsw);
        console.log(userIdAnonymous);
        console.log('returned');

        return;
      }

      console.log('fcm token process');

      await requestNotificationPermission();

      // getFCMToken();

      // if (global.notificationPermission) {
      //   await registerSW();
      // }

      await registerSW();

      await getFCMToken();
    })();
  }, [
    // firebaseUid, 
    // global.newsw, 
    newsw,
    userIdAnonymous
  ]);

  const updateWebTokenAnonymous = async () => {
    await updateDoc(
      doc(
        global.db, Collections.WebTokenAnonymous, global.userIdAnonymous,
      ),
      {
        // uniqueId: global.userIdAnonymous,

        swSub: typeof global.swSubscription === 'string' ? global.swSubscription : null,

        updatedAt: Date.now(),
      },
    );
  };

  useEffect(() => {
    if (navigator && navigator.serviceWorker) {
      navigator.serviceWorker.addEventListener('message', (e) => {
        console.log('client message event!');
        console.log(e);

        try {
          if (e && e.data.type === 'sw' && e.data.swSubscription) {
            console.log('set sw!');
            console.log(e.data.swSubscription);

            global.swSubscription = e.data.swSubscription;

            console.log(global.userIdAnonymous);

            if (global.userIdAnonymous) {
              updateWebTokenAnonymous();
            }
          }
          else if (e && e.data.type === 'swc' && e.data.swSubscription) {
            console.log('set swc!');
            console.log(e.data.swSubscription);

            global.swSubscription = e.data.swSubscription;

            console.log(global.userIdAnonymous);

            if (global.userIdAnonymous) {
              updateWebTokenAnonymous();
            }
          }
        }
        catch (ex) {
          console.error(ex);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (userIdAnonymous !== 'none' && userPhone === '' && outletId && outletId.uniqueId && firebaseAuth) {

      typeof global.subscriberListenToUserAnonymousChanges === 'function' && global.subscriberListenToUserAnonymousChanges();
      global.subscriberListenToUserAnonymousChanges = () => { };

      let subscriber = listenToUserAnonymousChanges(userIdAnonymous, outletId.uniqueId);

      global.subscriberListenToUserAnonymousChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
      // listenToUserChanges(firebaseUid, email, userPhone);

      // loadAsyncStorage();
    }
    else {
      typeof global.subscriberListenToUserAnonymousChanges === 'function' && global.subscriberListenToUserAnonymousChanges();
    }
  }, [userIdAnonymous, userPhone, outletId, firebaseAuth]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var userPointsTransactions = [];
      var userPointsBalance = 0;
      console.log('length finder', selectedCustomerPointsTransactionsEmail.length)


      for (var i = 0; i < selectedCustomerPointsTransactionsEmail.length; i++) {
        if (!userPointsTransactions.find(item => item.uniqueId === selectedCustomerPointsTransactionsEmail[i].uniqueId)) {
          // means not exist

          userPointsTransactions.push(selectedCustomerPointsTransactionsEmail[i]);
          userPointsBalance += selectedCustomerPointsTransactionsEmail[i].amount;
        }
      }

      for (var i = 0; i < selectedCustomerPointsTransactionsPhone.length; i++) {
        if (!userPointsTransactions.find(item => item.uniqueId === selectedCustomerPointsTransactionsPhone[i].uniqueId)) {
          // means not exist

          userPointsTransactions.push(selectedCustomerPointsTransactionsPhone[i]);
          userPointsBalance += selectedCustomerPointsTransactionsPhone[i].amount;
        }
      }

      userPointsTransactions.sort((a, b) => b.createdAt - a.createdAt);

      UserStore.update((s) => {
        s.userPointsTransactions = userPointsTransactions;
        s.userPointsBalance = parseFloat(userPointsBalance.toFixed(2));
      });
    });
  }, [
    selectedCustomerPointsTransactionsEmail,
    selectedCustomerPointsBalanceEmail,
    selectedCustomerPointsTransactionsPhone,
    selectedCustomerPointsBalancePhone,
  ]);

  // useEffect(() => {
  //   if (lat !== null && lng !== null) {
  //     listenToLocationChanges(lat, lng);
  //   }
  // }, [lat, lng]);

  useEffect(() => {
    if (selectedOutlet !== null
      // && email
      && firebaseAuth
    ) {
      // listenToSelectedOutletChanges(selectedOutlet, email);

      typeof global.subscriberListenToSelectedOutletChangesCore === 'function' && global.subscriberListenToSelectedOutletChangesCore();
      global.subscriberListenToSelectedOutletChangesCore = () => { };

      let subscriber = listenToSelectedOutletChangesCore(selectedOutlet, email);

      global.subscriberListenToSelectedOutletChangesCore = subscriber;

      return () => {
        // if (global.initCoreTimes >= 1) {
        //   // strange issue of calling this when go back, applied a hack first
        // }
        // else {
        //   global.initCoreTimes++;

        //   typeof subscriber === 'function' && subscriber();
        // }

        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [selectedOutlet, email, firebaseAuth]);

  useEffect(() => {
    if (selectedOutlet !== null
      // && email
      && !isSwitchingOutlets
      && firebaseAuth
    ) {
      // listenToSelectedOutletChanges(selectedOutlet, email);

      typeof global.subscriberListenToSelectedOutletChanges === 'function' && global.subscriberListenToSelectedOutletChanges();
      global.subscriberListenToSelectedOutletChanges = () => { };

      let subscriber = listenToSelectedOutletChanges(selectedOutlet, email, userPhone);

      global.subscriberListenToSelectedOutletChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [selectedOutlet, email, isSwitchingOutlets, firebaseAuth]);

  useEffect(() => {
    if (selectedOutletItem !== null &&
      selectedOutletItem !== undefined &&
      selectedOutletItem.uniqueId &&
      firebaseAuth) {
      // listenToSelectedOutletItemChanges(selectedOutletItem);

      typeof global.subscriberListenToSelectedOutletItemChanges === 'function' && global.subscriberListenToSelectedOutletItemChanges();
      global.subscriberListenToSelectedOutletItemChanges = () => { };

      let subscriber = listenToSelectedOutletItemChanges(selectedOutletItem);

      global.subscriberListenToSelectedOutletItemChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [selectedOutletItem, firebaseAuth]);

  useEffect(() => {
    listenToCommonChanges();

    // requestNotificationsPermission();

    // messaging().onMessage(async msg => {
    //   console.log('message from foreground!');
    //   console.log(msg);

    //   // parseMessages(msg);
    // });

    // messaging().onBackgroundMessage(async msg => {
    //   console.log('message from background!');
    //   console.log(msg);

    //   // parseMessages(msg);
    // });
  }, []);

  // useEffect(() => {
  //   if (selectedOutletTag !== null &&
  //     selectedOutletTag !== undefined &&
  //     selectedOutletTag.uniqueId) {
  //     listenToSelectedOutletTagChanges(selectedOutletTag);
  //   }
  // }, [selectedOutletTag]);

  // useEffect(() => {
  //   if (searchOutletText && searchOutletText.length > 0) {
  //     listenToSearchOutletTextChanges(searchOutletText);
  //   }
  // }, [searchOutletText]);

  // useEffect(() => {
  //   if (searchOutletMerchantId && searchOutletMerchantId.length > 0) {
  //     listenToSearchOutletMerchantIdChanges(searchOutletMerchantId);
  //   }
  // }, [searchOutletMerchantId]);

  useEffect(() => {
    if (selectedOutletTableId && selectedOutletTableId.length > 0 && orderType === ORDER_TYPE.DINEIN && firebaseAuth) {
      listenToSelectedOutletTableIdChanges(selectedOutletTableId, (selectedOutlet && selectedOutlet.toggleOpenOrder) ? selectedOutlet.toggleOpenOrder : false);

      listenToJoinedOrSplittedTableChanges(selectedOutletTableId);
    }
  }, [selectedOutletTableId, orderType, firebaseAuth, selectedOutlet]);

  useEffect(() => {
    if (userIdAnonymous && userIdAnonymous.length > 0 && selectedOutlet && userIdAnonymous !== 'none' &&
      selectedOutlet && selectedOutlet.uniqueId && firebaseAuth) {
      listenToUserIdAnonymousChanges(userIdAnonymous, selectedOutlet.uniqueId);

      listenToUserIdAnonymousChangesStatic(userIdAnonymous, selectedOutlet.uniqueId);
    }
    else {
      CommonStore.update(s => {
        s.selectedUserOrderAnonymous = [];
      });
    }
  }, [userIdAnonymous, selectedOutlet, firebaseAuth]);

  // useEffect(() => {
  //   // combine merchant's beer docket with user's redeemed beer docket record, to merge user's action changes (redeemed mug, extended days, etc)

  //   var userBeerDocketsTemp = [];
  //   var selectedUserBeerDocketTemp = {};

  //   for (var i = 0; i < beerDockets.length; i++) {
  //     var record = {
  //       ...beerDockets[i],
  //       beerDocketId: beerDockets[i].uniqueId,
  //     };

  //     if (beerDocketsRedemptionsBDDict[record.beerDocketId]) {
  //       record = {
  //         ...record,
  //         ...beerDocketsRedemptionsBDDict[record.uniqueId], // extend the docket default data with user's own data
  //         userBeerDocketId: beerDocketsRedemptionsBDDict[record.uniqueId].uniqueId,
  //       };
  //     }

  //     userBeerDocketsTemp.push(record);

  //     if (record.beerDocketId === selectedUserBeerDocket.beerDocketId) {
  //       selectedUserBeerDocketTemp = record;
  //     }
  //   };

  //   console.log('changed userBeerDockets!');
  //   console.log(userBeerDocketsTemp);

  //   CommonStore.update(s => {
  //     s.userBeerDockets = userBeerDocketsTemp;

  //     if (selectedUserBeerDocket && selectedUserBeerDocket.beerDocketId) {
  //       s.selectedUserBeerDocket = selectedUserBeerDocketTemp;
  //     }
  //   });
  // }, [beerDockets, beerDocketsRedemptionsBDDict]);

  const timestampPromotion = CommonStore.useState(s => s.timestampPromotion);

  // useEffect(() => {
  //   setInterval(() => {
  //     CommonStore.update(s => {
  //       s.timestampPromotion = Date.now();
  //     });
  //   }, 60000);
  // }, []);

  ///////////////////////////////////////////////////////////////

  // 2023-02-13 - Upselling campaigns

  const selectedOutletUpsellingCampaigns = CommonStore.useState(s => s.selectedOutletUpsellingCampaigns);

  useEffect(() => {
    //console.log('selectedOutletUpsellingCampaigns',selectedOutletUpsellingCampaigns)
    var availableUpsellingCampaignsTemp = [];

    for (var i = 0; i < selectedOutletUpsellingCampaigns.length; i++) {
      var promoTimeStart = moment().set({
        hour: moment(selectedOutletUpsellingCampaigns[i].promoTimeStart).hour(),
        minute: moment(selectedOutletUpsellingCampaigns[i].promoTimeStart).minute(),
      });

      var promoTimeEnd = moment().set({
        hour: moment(selectedOutletUpsellingCampaigns[i].promoTimeEnd).hour(),
        minute: moment(selectedOutletUpsellingCampaigns[i].promoTimeEnd).minute(),
      });

      if (moment().isSameOrAfter(selectedOutletUpsellingCampaigns[i].promoDateStart, 'day')
        && moment().isSameOrBefore(selectedOutletUpsellingCampaigns[i].promoDateEnd, 'day') &&
        moment().isSameOrAfter(promoTimeStart) &&
        moment().isBefore(promoTimeEnd) &&
        selectedOutletUpsellingCampaigns[i].effectiveTypeOptions &&
        selectedOutletUpsellingCampaigns[i].effectiveTypeOptions.length > 0 &&
        selectedOutletUpsellingCampaigns[i].effectiveTypeOptions.find(day => {
          if (day === moment().format('dddd').toUpperCase()) {
            return true;
          }
        })
      ) {
        //console.log('PASSED 1')
        var isValid = false;

        if (selectedOutletUpsellingCampaigns[i].orderTypes && selectedOutletUpsellingCampaigns[i].orderTypes.includes(orderType)) {
          // included, can proceed
          //console.log('PASSED 2')
        }
        else {
          // not included, skip

          continue;
        }

        if (selectedOutletUpsellingCampaigns[i].storeTypes === undefined ||
          (
            selectedOutletUpsellingCampaigns[i].storeTypes &&
            selectedOutletUpsellingCampaigns[i].storeTypes.length > 0 &&
            selectedOutletUpsellingCampaigns[i].storeTypes.includes(STORE_TYPE.OFFLINE)
          )) {
          // included, can proceed
          //console.log('PASSED 3')
        }
        else {
          // not included, skip

          continue;
        }

        // for (let targetGroupIndex = 0; targetGroupIndex < selectedOutletUpsellingCampaigns[i].targetSegmentGroupList.length; targetGroupIndex++) {
        //   const targetUserGroup = selectedOutletUpsellingCampaigns[i].targetSegmentGroupList[targetGroupIndex];

        //   if (userGroups.includes(targetUserGroup)) {
        //     isValid = true;
        //     break;
        //   }

        //   if (selectedOutletCRMTagsDict[targetUserGroup]) {
        //     const currCrmUserTag = selectedOutletCRMTagsDict[targetUserGroup];

        //     if (currCrmUserTag.emailList.includes(email)) {
        //       // means got

        //       isValid = true;
        //       break;
        //     }
        //   }
        // }

        for (var j = 0; j < selectedOutletUpsellingCampaigns[i].targetSegmentGroupList.length; j++) {
          var crmSegment = selectedOutletCRMSegmentsDict ? selectedOutletCRMSegmentsDict[selectedOutletUpsellingCampaigns[i].targetSegmentGroupList[j]] : null;

          if (selectedOutletUpsellingCampaigns[i].targetSegmentGroupList[j] === 'EVERYONE') {
            isValid = true;
            break;
          }
          else {
            if (crmSegment) {
              var { crmUserTagIdList } = crmSegment;

              for (var k = 0; k < crmUserTagIdList.length; k++) {
                var crmUserTag = selectedOutletCRMTagsDict[crmUserTagIdList[k]];

                if (crmUserTag) {
                  if (crmUserTag.emailList.includes(email) ||
                    crmUserTag.phoneList.includes(userPhone)) {
                    isValid = true;
                    break;
                  }
                }
              }
            }

            if (isValid) {
              break;
            }
          }
        }

        if (isValid) {
          //console.log('PASSED 5')
          availableUpsellingCampaignsTemp.push(selectedOutletUpsellingCampaigns[i]);
        }
      }
    }

    /////////////////////////////////////////////////////////////////////////

    CommonStore.update(s => {
      s.availableUpsellingCampaigns = availableUpsellingCampaignsTemp;
    });
  }, [
    selectedOutletUpsellingCampaigns,

    // selectedOutletPromotions,
    // selectedOutletPointsRedeemPackages,
    userGroups,
    email,

    selectedOutletCRMTagsDict,
    selectedOutletCRMSegmentsDict,
    selectedOutletCRMUser,

    // selectedOutletLoyaltyCampaigns,
    // selectedOutletUserLoyaltyCampaigns,

    timestampPromotion,
  ]);

  ///////////////////////////////////////////////////////////////

  useEffect(() => {
    var availablePromotionsTemp = [];
    var availablePromoCodePromotionsTemp = [];

    var availablePointsRedeemPackagesTemp = [];
    var availableLoyaltyCampaignsTemp = [];

    for (var i = 0; i < selectedOutletPromotions.length; i++) {
      console.log('selectedOutletPromotions[i]');
      console.log(selectedOutletPromotions[i]);

      var promoTimeStart = moment().set({
        hour: moment(selectedOutletPromotions[i].promoTimeStart).hour(),
        minute: moment(selectedOutletPromotions[i].promoTimeStart).minute(),
      });

      var promoTimeEnd = moment().set({
        hour: moment(selectedOutletPromotions[i].promoTimeEnd).hour(),
        minute: moment(selectedOutletPromotions[i].promoTimeEnd).minute(),
      });

      console.log(moment().isSameOrAfter(selectedOutletPromotions[i].promoDateStart, 'day'));
      console.log(moment().isSameOrBefore(selectedOutletPromotions[i].promoDateEnd, 'day'));
      console.log(moment().isSameOrAfter(promoTimeStart));
      console.log(moment().isBefore(promoTimeEnd));
      console.log(selectedOutletPromotions[i].effectiveTypeOptions);

      if (moment().isSameOrAfter(selectedOutletPromotions[i].promoDateStart, 'day')
        && moment().isSameOrBefore(selectedOutletPromotions[i].promoDateEnd, 'day') &&
        moment().isSameOrAfter(promoTimeStart) &&
        moment().isBefore(promoTimeEnd) &&
        selectedOutletPromotions[i].effectiveTypeOptions &&
        selectedOutletPromotions[i].effectiveTypeOptions.length > 0 &&
        selectedOutletPromotions[i].effectiveTypeOptions.find(day => {
          if (day === moment().format('dddd').toUpperCase()) {
            return true;

            // isValid = true;
          }
        })
      ) {
        console.log('selectedOutletPromotions[i]');
        console.log(selectedOutletPromotions[i]);

        var isValid = false;

        if (selectedOutletPromotions[i].orderTypes && selectedOutletPromotions[i].orderTypes.includes(orderType)) {
          // included, can proceed
        }
        else {
          if (selectedOutletPromotions[i].orderTypes &&
            (
              (
                orderType === ORDER_TYPE.DINEIN &&
                selectedOutletPromotions[i].orderTypes.includes(CHANNEL_TYPE.DINEIN_QR)
              )
              ||
              (
                orderType === ORDER_TYPE.PICKUP &&
                selectedOutletPromotions[i].orderTypes.includes(CHANNEL_TYPE.PICKUP_QR)
              )
            )
          ) {
            // included, can proceed
          }
          else {
            // not included, skip

            continue;
          }
        }

        // if (selectedOutletPromotions[i].storeTypes === undefined ||
        //   (
        //     selectedOutletPromotions[i].storeTypes &&
        //     selectedOutletPromotions[i].storeTypes.length > 0 &&
        //     selectedOutletPromotions[i].storeTypes.includes(STORE_TYPE.OFFLINE)
        //   )) {
        //   // included, can proceed
        // }
        // else {
        //   // not included, skip

        //   continue;
        // }

        ///////////////////////////////////////////////////////////////////

        if (selectedOutletPromotions[i].usePromoCode) {
          if (!userPhone) {
            // skip this promo, if the current user didn't have phone number available

            continue;
          }
        }

        ///////////////////////////////////////////////////////////////////

        // for (let targetGroupIndex = 0; targetGroupIndex < selectedOutletPromotions[i].targetSegmentGroupList.length; targetGroupIndex++) {
        //   const targetUserGroup = selectedOutletPromotions[i].targetSegmentGroupList[targetGroupIndex];

        //   if (userGroups.includes(targetUserGroup)) {
        //     isValid = true;
        //     break;
        //   }

        //   if (selectedOutletCRMTagsDict[targetUserGroup]) {
        //     const currCrmUserTag = selectedOutletCRMTagsDict[targetUserGroup];

        //     if (currCrmUserTag.emailList.includes(email)) {
        //       // means got

        //       isValid = true;
        //       break;
        //     }
        //   }
        // }

        for (var j = 0; j < selectedOutletPromotions[i].targetSegmentGroupList.length; j++) {
          var crmSegment = selectedOutletCRMSegmentsDict ? selectedOutletCRMSegmentsDict[selectedOutletPromotions[i].targetSegmentGroupList[j]] : null;

          if (selectedOutletPromotions[i].targetSegmentGroupList[j] === 'EVERYONE') {
            isValid = true;
            break;
          }
          else {
            if (crmSegment) {
              var { crmUserTagIdList } = crmSegment;

              for (var k = 0; k < crmUserTagIdList.length; k++) {
                var crmUserTag = selectedOutletCRMTagsDict[crmUserTagIdList[k]];

                if (crmUserTag) {
                  if (crmUserTag.emailList.includes(email) ||
                    crmUserTag.phoneList.includes(userPhone)) {
                    isValid = true;
                    break;
                  }
                }
              }
            }

            if (isValid) {
              break;
            }
          }
        }

        if (global.crmUser && global.crmUser.uniqueId) {
          if (isValid) {
            if (selectedOutletPromotions[i].loyaltyTierOrderIndexList === undefined) {
              isValid = true;
            }
            else {
              if (selectedOutletPromotions[i].loyaltyTierOrderIndexList.length === 0) {
                isValid = true;
              }
              else if (global.crmUser && (
                global.crmUser.levelOrderIndex === undefined
                ||
                global.crmUser.levelOrderIndex === -1
              ) &&
                selectedOutletPromotions[i].loyaltyTierOrderIndexList.includes('no-tier')
              ) {
                isValid = true;
              }
              else if (global.crmUser && (
                typeof global.crmUser.levelOrderIndex === 'number'
              ) &&
                selectedOutletPromotions[i].loyaltyTierOrderIndexList.includes(global.crmUser.levelOrderIndex)
              ) {
                isValid = true;
              }
              else {
                isValid = false;
              }
            }
          }
        }
        else {
          if (isValid) {
            if (selectedOutletPromotions[i].loyaltyTierOrderIndexList === undefined) {
              isValid = true;
            }
            else {
              if (selectedOutletPromotions[i].loyaltyTierOrderIndexList.length === 0) {
                isValid = true;
              }
              else if (selectedOutletPromotions[i].loyaltyTierOrderIndexList.includes('no-tier')) {
                isValid = true;
              }
              else {
                isValid = false;
              }
            }
          }
        }

        if (isValid) {
          if (selectedOutletPromotions[i].usePromoCode) {
            if (selectedPromoCodePromotion && selectedPromoCodePromotion.uniqueId === selectedOutletPromotions[i].uniqueId) {
              availablePromotionsTemp.push(selectedOutletPromotions[i]);
            }

            availablePromoCodePromotionsTemp.push(selectedOutletPromotions[i]);
          }
          else {
            availablePromotionsTemp.push(selectedOutletPromotions[i]);
          }
        }
      }
    }

    availablePromotionsTemp.sort((a, b) => {
      return a.usePromoCode - b.usePromoCode;
    });

    // for (var i = 0; i < selectedOutletPointsRedeemPackages.length; i++) {
    //   if (moment().isSameOrAfter(selectedOutletPointsRedeemPackages[i].startDate) && moment().isBefore(selectedOutletPointsRedeemPackages[i].endDate)) {
    //     var isValid = false;

    //     if (userGroups.includes(selectedOutletPointsRedeemPackages[i].targetUserGroup)) {
    //       isValid = true;
    //     }

    //     if (selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup]) {
    //       const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup];

    //       if (currCrmUserTag.emailList.includes(email)) {
    //         // means got

    //         isValid = true;
    //       }
    //     }

    //     //////////////////////////////////

    //     if (selectedOutletCRMUser && selectedOutletCRMUser.pointsRedeemPackageDisableDict) {
    //       if (selectedOutletCRMUser.pointsRedeemPackageDisableDict[selectedOutletPointsRedeemPackages[i].uniqueId]) {
    //         isValid = false;
    //       }
    //     }

    //     //////////////////////////////////

    //     if (isValid) {
    //       availablePointsRedeemPackagesTemp.push(selectedOutletPointsRedeemPackages[i]);
    //     }
    //   }
    // }

    /////////////////////////////////////////////////////////////////////////

    // var combinedLoyaltyCampaigns = [];
    // for (var i = 0; i < selectedOutletUserLoyaltyCampaigns.length; i++) {
    //   for (var j = 0; j < selectedOutletLoyaltyCampaigns.length; j++) {
    //     if (selectedOutletUserLoyaltyCampaigns[i].loyaltyCampaignId === selectedOutletLoyaltyCampaigns[i].uniqueId &&
    //       selectedOutletUserLoyaltyCampaigns[i].redeemDate === null) {
    //       // means this user got the 'voucher' for this campaign

    //       combinedLoyaltyCampaigns.push({
    //         ...selectedOutletLoyaltyCampaigns[j],
    //         ...selectedOutletUserLoyaltyCampaigns[i],
    //       });

    //       break;
    //     }
    //   }
    // }

    /////////////////////////////////////////////////////////////////////////

    CommonStore.update(s => {
      s.availablePromotions = availablePromotionsTemp;
      s.availablePromoCodePromotions = availablePromoCodePromotionsTemp;

      s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
      // s.availableLoyaltyCampaigns = availableLoyaltyCampaignsTemp;
    });
  }, [
    selectedOutletPromotions,
    selectedOutletPointsRedeemPackages,
    userGroups,
    email,

    userPhone,

    selectedOutletCRMTagsDict,
    selectedOutletCRMSegmentsDict,
    selectedOutletCRMUser,

    selectedOutletLoyaltyCampaigns,
    selectedOutletUserLoyaltyCampaigns,

    timestampPromotion,

    selectedPromoCodePromotion,

    orderType,
    global.crmUser
  ]);

  /////////////////////////////////////

  useEffect(() => {
    var availableLoyaltyCampaignsTemp = [];

    let combinedLoyaltyCampaigns = selectedOutletLoyaltyCampaigns;

    for (var i = 0; i < combinedLoyaltyCampaigns.length; i++) {
      if (combinedLoyaltyCampaigns[i].orderTypes &&
        // typeof combinedLoyaltyCampaigns[i].orderTypes === "string" &&
        combinedLoyaltyCampaigns[i].orderTypes.includes(orderType)) {
        var isValid = false;

        // for (let targetGroupIndex = 0; targetGroupIndex < combinedLoyaltyCampaigns[i].targetSegmentGroupList.length; targetGroupIndex++) {
        //   const targetUserGroup = combinedLoyaltyCampaigns[i].targetSegmentGroupList[targetGroupIndex];

        //   if (userGroups.includes(targetUserGroup)) {
        //     isValid = true;
        //     break;
        //   }

        //   if (selectedOutletCRMTagsDict[targetUserGroup]) {
        //     const currCrmUserTag = selectedOutletCRMTagsDict[targetUserGroup];

        //     if (currCrmUserTag.emailList.includes(email)) {
        //       // means got

        //       isValid = true;
        //       break;
        //     }
        //   }
        // }

        for (var j = 0; j < combinedLoyaltyCampaigns[i].targetSegmentGroupList.length; j++) {
          var crmSegment = selectedOutletCRMSegmentsDict ? selectedOutletCRMSegmentsDict[combinedLoyaltyCampaigns[i].targetSegmentGroupList[j]] : null;

          if (combinedLoyaltyCampaigns[i].targetSegmentGroupList[j] === 'EVERYONE') {
            isValid = true;
            break;
          }
          else {
            if (crmSegment) {
              var { crmUserTagIdList } = crmSegment;

              for (var k = 0; k < crmUserTagIdList.length; k++) {
                var crmUserTag = selectedOutletCRMTagsDict[crmUserTagIdList[k]];

                if (crmUserTag) {
                  if (crmUserTag.emailList.includes(email) ||
                    crmUserTag.phoneList.includes(userPhone)) {
                    isValid = true;
                    break;
                  }
                }
              }
            }

            if (isValid) {
              break;
            }
          }
        }

        if (isValid) {
          if (moment().isSameOrAfter(combinedLoyaltyCampaigns[i].promoDateStart, 'day')
            && moment().isSameOrBefore(combinedLoyaltyCampaigns[i].promoDateEnd, 'day') &&
            combinedLoyaltyCampaigns[i].effectiveTypeOptions &&
            combinedLoyaltyCampaigns[i].effectiveTypeOptions.length > 0 &&
            combinedLoyaltyCampaigns[i].effectiveTypeOptions.find(day => {
              if (day === moment().format('dddd').toUpperCase()) {
                return true;
              }
            })
          ) {
            // 2024-02-28 - only return campaign will be used

            if (combinedLoyaltyCampaigns[i].loyaltyCampaignType === LOYALTY_CAMPAIGN_TYPE.RETURN) {
              availableLoyaltyCampaignsTemp.push(combinedLoyaltyCampaigns[i]);
            }
          }

          // availableLoyaltyCampaignsTemp.push(combinedLoyaltyCampaigns[i]);
        }
      }
    }

    CommonStore.update(s => {
      s.availableLoyaltyCampaigns = availableLoyaltyCampaignsTemp;
    });
  }, [
    userGroups,
    email,

    userPhone,

    selectedOutletCRMTagsDict,
    selectedOutletCRMSegmentsDict,
    selectedOutletCRMUser,

    selectedOutletLoyaltyCampaigns,

    orderType,
  ]);

  /////////////////////////////////////

  useEffect(() => {
    var overrideItemPriceSkuDictTemp = {};
    var amountOffItemSkuDictTemp = {};
    var percentageOffItemSkuDictTemp = {};
    var buy1Free1ItemSkuDictTemp = {};
    var deliveryItemSkuDictTemp = {};
    var takeawayItemSkuDictTemp = {};

    var overrideCategoryPriceNameDictTemp = {};
    var amountOffCategoryNameDictTemp = {};
    var percentageOffCategoryNameDictTemp = {};
    var buy1Free1CategoryNameDictTemp = {};
    var deliveryCategoryNameDictTemp = {};
    var takeawayCategoryNameDictTemp = {};

    for (var i = 0; i < availablePromotions.length; i++) {
      if (availablePromotions[i].promotionType === PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              overrideItemPriceSkuDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                overridePrice: criteria.priceBeforeTax,

                minSpend: availablePromotions[i].minSpend || 0,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
          else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              overrideCategoryPriceNameDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                overridePrice: criteria.priceBeforeTax,

                minSpend: availablePromotions[i].minSpend || 0,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };;
            }
          }
        }
      }
      else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_AMOUNT_OFF) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                amountOff: criteria.amountOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',

                minPriceToDiscounted: criteria.minPriceToDiscounted ? criteria.minPriceToDiscounted : 0,

                variationRp: criteria.variationRp ? criteria.variationRp : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                variationItemsRp: criteria.variationItemsRp ? criteria.variationItemsRp : [],
                variationItemsSkuRp: criteria.variationItemsSkuRp ? criteria.variationItemsSkuRp : [],
                qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                typeRp: criteria.typeRp ? criteria.typeRp : CONDITION_TYPE.AND,

                ...availablePromotions[i].applyAllMenu && {
                  applyAllMenu: availablePromotions[i].applyAllMenu ? availablePromotions[i].applyAllMenu : false,

                  type: PROMOTION_TYPE.TAKE_AMOUNT_OFF,
                  uniqueId: availablePromotions[i].uniqueId ? availablePromotions[i].uniqueId : '',
                },
              };
            }
          }
          else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              amountOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                amountOff: criteria.amountOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',

                minPriceToDiscounted: criteria.minPriceToDiscounted ? criteria.minPriceToDiscounted : 0,

                variationRp: criteria.variationRp ? criteria.variationRp : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                variationItemsRp: criteria.variationItemsRp ? criteria.variationItemsRp : [],
                variationItemsSkuRp: criteria.variationItemsSkuRp ? criteria.variationItemsSkuRp : [],
                qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                typeRp: criteria.typeRp ? criteria.typeRp : CONDITION_TYPE.AND,

                ...availablePromotions[i].applyAllMenu && {
                  applyAllMenu: availablePromotions[i].applyAllMenu ? availablePromotions[i].applyAllMenu : false,

                  type: PROMOTION_TYPE.TAKE_AMOUNT_OFF,
                  uniqueId: availablePromotions[i].uniqueId ? availablePromotions[i].uniqueId : '',
                },
              };
            }
          }
        }
      }
      else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                percentageOff: criteria.percentageOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',

                minPriceToDiscounted: criteria.minPriceToDiscounted ? criteria.minPriceToDiscounted : 0,

                variationRp: criteria.variationRp ? criteria.variationRp : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                variationItemsRp: criteria.variationItemsRp ? criteria.variationItemsRp : [],
                variationItemsSkuRp: criteria.variationItemsSkuRp ? criteria.variationItemsSkuRp : [],
                qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                typeRp: criteria.typeRp ? criteria.typeRp : CONDITION_TYPE.AND,

                ...availablePromotions[i].applyAllMenu && {
                  applyAllMenu: availablePromotions[i].applyAllMenu ? availablePromotions[i].applyAllMenu : false,

                  type: PROMOTION_TYPE.TAKE_PERCENTAGE_OFF,
                  uniqueId: availablePromotions[i].uniqueId ? availablePromotions[i].uniqueId : '',
                },
              };
            }
          }
          else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              percentageOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                percentageOff: criteria.percentageOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',

                minPriceToDiscounted: criteria.minPriceToDiscounted ? criteria.minPriceToDiscounted : 0,

                variationRp: criteria.variationRp ? criteria.variationRp : PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                variationItemsRp: criteria.variationItemsRp ? criteria.variationItemsRp : [],
                variationItemsSkuRp: criteria.variationItemsSkuRp ? criteria.variationItemsSkuRp : [],
                qtyRp: criteria.qtyRp ? criteria.qtyRp : 0,

                typeRp: criteria.typeRp ? criteria.typeRp : CONDITION_TYPE.AND,

                ...availablePromotions[i].applyAllMenu && {
                  applyAllMenu: availablePromotions[i].applyAllMenu ? availablePromotions[i].applyAllMenu : false,

                  type: PROMOTION_TYPE.TAKE_PERCENTAGE_OFF,
                  uniqueId: availablePromotions[i].uniqueId ? availablePromotions[i].uniqueId : '',
                },
              };
            }
          }
        }
      }
      else if (availablePromotions[i].promotionType === PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.buyVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
              buy1Free1ItemSkuDictTemp[criteria.buyVariationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                buyAmount: criteria.buyAmount,
                getAmount: criteria.getAmount,
                getPrice: criteria.getPrice,
                getVariation: criteria.getVariation,
                getVariationItems: criteria.getVariationItems,
                getVariationItemsSku: criteria.getVariationItemsSku,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
          else if (criteria.buyVariation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
              buy1Free1CategoryNameDictTemp[criteria.buyVariationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                buyAmount: criteria.buyAmount,
                getAmount: criteria.getAmount,
                getPrice: criteria.getPrice,
                getVariation: criteria.getVariation,
                getVariationItems: criteria.getVariationItems,
                getVariationItemsSku: criteria.getVariationItemsSku,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
        }
      }
      else if (availablePromotions[i].promotionType === PROMOTION_TYPE.DELIVERY) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              deliveryItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                deliveryDiscountAboveAmount: criteria.deliveryDiscountAboveAmount,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
          else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              deliveryCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                deliveryDiscountAboveAmount: criteria.deliveryDiscountAboveAmount,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
        }
      }
      else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKEAWAY) {
        for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
          const criteria = availablePromotions[i].criteriaList[j];

          if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              takeawayItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                takeawayFreeFlag: criteria.takeawayFreeFlag,
                takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                takeawayDiscountAboveAmount: criteria.takeawayDiscountAboveAmount,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
          else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            for (var k = 0; k < criteria.variationItemsSku.length; k++) {
              takeawayCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                promotionId: availablePromotions[i].uniqueId,
                campaignName: availablePromotions[i].campaignName,
                takeawayFreeFlag: criteria.takeawayFreeFlag,
                takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                takeawayDiscountAboveAmount: criteria.takeawayDiscountAboveAmount,

                applyBefore: availablePromotions[i].applyBefore || APPLY_BEFORE.ORDER_PLACED,
                applyDiscountPer: availablePromotions[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                usePromoCode: availablePromotions[i].usePromoCode || false,
                promoCode: availablePromotions[i].promoCode || '',
              };
            }
          }
        }
      }
    }

    CommonStore.update(s => {
      s.overrideItemPriceSkuDict = overrideItemPriceSkuDictTemp;
      s.amountOffItemSkuDict = amountOffItemSkuDictTemp;
      s.percentageOffItemSkuDict = percentageOffItemSkuDictTemp;
      s.buy1Free1ItemSkuDict = buy1Free1ItemSkuDictTemp;
      s.deliveryItemSkuDict = deliveryItemSkuDictTemp;
      s.takeawayItemSkuDict = takeawayItemSkuDictTemp;

      s.overrideCategoryPriceNameDict = overrideCategoryPriceNameDictTemp;
      s.amountOffCategoryNameDict = amountOffCategoryNameDictTemp;
      s.percentageOffCategoryNameDict = percentageOffCategoryNameDictTemp;
      s.buy1Free1CategoryNameDict = buy1Free1CategoryNameDictTemp;
      s.deliveryCategoryNameDict = deliveryCategoryNameDictTemp;
      s.takeawayCategoryNameDict = takeawayCategoryNameDictTemp;
    });
  }, [availablePromotions]);

  useEffect(() => {
    var pointsRedeemItemSkuDict = {};
    var pointsRedeemCategoryNameDict = {};

    for (var i = 0; i < availablePointsRedeemPackages.length; i++) {
      const pointsRedeemPackage = availablePointsRedeemPackages[i];

      if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
        for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
          pointsRedeemItemSkuDict[pointsRedeemPackage.variationItemsSku[k]] = {
            packageId: pointsRedeemPackage.uniqueId,
            limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
            conversionCurrency: pointsRedeemPackage.conversionCurrency,
            conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
            conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
          };
        }
      }
      else if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
        for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
          pointsRedeemCategoryNameDict[pointsRedeemPackage.variationItemsSku[k]] = {
            packageId: pointsRedeemPackage.uniqueId,
            limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
            conversionCurrency: pointsRedeemPackage.conversionCurrency,
            conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
            conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
          };
        }
      }
    }

    CommonStore.update(s => {
      s.pointsRedeemItemSkuDict = pointsRedeemItemSkuDict;
      s.pointsRedeemCategoryNameDict = pointsRedeemCategoryNameDict;
    });
  }, [availablePointsRedeemPackages]);

  useEffect(() => {
    var loyaltyStampGetItemSkuDict = {};
    var loyaltyStampGetCategoryNameDict = {};
    var loyaltyStampBuyItemSkuDict = {};
    var loyaltyStampBuyCategoryNameDict = {};

    for (var i = 0; i < selectedOutletLoyaltyStamps.length; i++) {

      if (moment().isSameOrAfter(selectedOutletLoyaltyStamps[i].startDate, 'day')
        && moment().isSameOrBefore(selectedOutletLoyaltyStamps[i].endDate, 'day')) {
        const loyaltyStamp = selectedOutletLoyaltyStamps[i];

        for (var j = 0; j < loyaltyStamp.lsItems.length; j++) {
          if (loyaltyStamp.lsItems[j].variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            loyaltyStampGetItemSkuDict[loyaltyStamp.lsItems[j].outletItemSku] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItems[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItems[j].noOfStamp,
              quantity: loyaltyStamp.lsItems[j].quantity,

              itemSku: loyaltyStamp.lsItems[j].outletItemSku,

              price: (typeof loyaltyStamp.lsItems[j].price === 'number') ? loyaltyStamp.lsItems[j].price : 0,
            };
          }
          else if (loyaltyStamp.lsItems[j].variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            loyaltyStampGetCategoryNameDict[loyaltyStamp.lsItems[j].itemName] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItems[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItems[j].noOfStamp,
              quantity: loyaltyStamp.lsItems[j].quantity,

              categoryId: loyaltyStamp.lsItems[j].outletItemSku,

              price: (typeof loyaltyStamp.lsItems[j].price === 'number') ? loyaltyStamp.lsItems[j].price : 0,
            };
          }
        }

        for (var j = 0; j < loyaltyStamp.lsItemsBuy.length; j++) {
          if (loyaltyStamp.lsItemsBuy[j].variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
            loyaltyStampBuyItemSkuDict[loyaltyStamp.lsItemsBuy[j].outletItemSku] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItemsBuy[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItemsBuy[j].noOfStamp,
              quantity: loyaltyStamp.lsItemsBuy[j].quantity,

              itemSku: loyaltyStamp.lsItemsBuy[j].outletItemSku,
            };
          }
          else if (loyaltyStamp.lsItemsBuy[j].variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
            loyaltyStampBuyCategoryNameDict[loyaltyStamp.lsItemsBuy[j].itemName] = {
              loyaltyStampId: loyaltyStamp.uniqueId,
              lsItemId: loyaltyStamp.lsItemsBuy[j].lsItemId,
              noOfStamp: loyaltyStamp.lsItemsBuy[j].noOfStamp,
              quantity: loyaltyStamp.lsItemsBuy[j].quantity,

              categoryId: loyaltyStamp.lsItemsBuy[j].outletItemSku,
            };
          }
        }
      }
    }

    CommonStore.update(s => {
      s.loyaltyStampGetItemSkuDict = loyaltyStampGetItemSkuDict;
      s.loyaltyStampGetCategoryNameDict = loyaltyStampGetCategoryNameDict;
      s.loyaltyStampBuyItemSkuDict = loyaltyStampBuyItemSkuDict;
      s.loyaltyStampBuyCategoryNameDict = loyaltyStampBuyCategoryNameDict;
    });
  }, [selectedOutletLoyaltyStamps]);

  /////////////////////////////////////////////////////

  useEffect(() => {
    if (nUserLoyaltyCampaignInform && nUserLoyaltyCampaignInform.type) {
      // redirectToLoyaltyCampaignList(nUserLoyaltyCampaignInform.loyaltyCampaignId);
      redirectToLoyaltyCampaignList(nUserLoyaltyCampaignInform.outletId);
    }
  }, [nUserLoyaltyCampaignInform]);

  /////////////////////////////////////////////////////////

  // 2022-08-04 - Vouchers support

  useEffect(() => {
    let availableTaggableVouchersTemp = [];

    if (userTaggableVouchers) {
      userTaggableVouchers.forEach(userVoucher => {
        taggableVouchers.forEach(taggableVoucher => {
          if (taggableVoucher.orderTypes?.includes(orderType) ||
            (orderType === ORDER_TYPE.DINEIN && taggableVoucher.orderTypes?.includes(CHANNEL_TYPE.DINEIN_QR)) ||
            (orderType === ORDER_TYPE.PICKUP && taggableVoucher.orderTypes?.includes(CHANNEL_TYPE.PICKUP_QR))) {

            if (userVoucher.voucherId === taggableVoucher.uniqueId && userVoucher.redeemDate === null) {
              if (
                // userVoucher.outletIdList?.includes(outletId.uniqueId)
                userVoucher.outletId == outletId.uniqueId ||
                userVoucher.isMasterAccount
              ) {

                availableTaggableVouchersTemp.push({
                  ...taggableVoucher,
                  ...userVoucher,

                  voucherId: taggableVoucher.uniqueId,

                  variationExclude: taggableVoucher.variationExclude || PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
                  variationItemsExclude: taggableVoucher.variationItemsExclude || [],
                  variationItemsSkuExclude: taggableVoucher.variationItemsSkuExclude || [],
                });
              }
            }
          }
        });
      });
    }

    // 2024-10-18 - show to redeem voucher by soon to expired first
    availableTaggableVouchersTemp.sort((a, b) => moment(a.expirationDate) - moment(b.expirationDate));

    CommonStore.update(s => {
      s.availableUserTaggableVouchers = availableTaggableVouchersTemp;
    });
  }, [outletId, taggableVouchers, userTaggableVouchers, orderType]);

  /////////////////////////////////////////////////////////

  // 2023-09-14 - Determine the available voucher that can claim, for anonymous user

  useEffect(() => {
    (async () => {
      var rewardsModalVisiblity = false;
      var claimVoucherAnonymousTemp = {};

      if (
        // global.userIdAnonymousLoaded && global.userPhoneLoaded
        global.userPhoneLoaded
      ) {
        // proceed when the 2 states is fully loaded

        if (userIdAnonymous !== 'none' && userPhone === '') {
          // only proceed when the userPhone is empty

          if (anonymousPointsBalance > 0 && claimVoucherList.length > 0) {
            // only proceed when the anonymous user's points balance > 0

            let taggableVouchersSorted = claimVoucherList.slice(0).sort((a, b) =>
              (b.voucherPointsRequired ? b.voucherPointsRequired : 0) - (a.voucherPointsRequired ? a.voucherPointsRequired : 0)
            );

            for (let i = 0; i < taggableVouchersSorted.length; i++) {
              if (taggableVouchersSorted[i].voucherPointsRequired > 0 &&
                taggableVouchersSorted[i].voucherPointsRequired <= anonymousPointsBalance &&
                taggableVouchersSorted[i].voucherQuantity > 0) {
                // means can be claimed/purchase by the current anonymous user

                claimVoucherAnonymousTemp = taggableVouchersSorted[i];
                rewardsModalVisiblity = true;

                const notificationAnonymousSnapshot = await getDocs(
                  query(
                    collection(global.db, Collections.NotificationAnonymous),
                    where('anonymousUserId', '==', userIdAnonymous),
                    where('taggableVoucherId', '==', claimVoucherAnonymousTemp.uniqueId),
                    limit(1),
                  )
                );

                if (notificationAnonymousSnapshot.empty) {
                  // for testing
                  // const milisecondsToAdd = 120000; // 2 mins
                  const milisecondsToAdd = 60000; // 1 min

                  // const milisecondsToAdd = 21600000; // 6 hours

                  const uniqueId = uuidv4();
                  await setDoc(
                    doc(
                      global.db, Collections.NotificationAnonymous, uniqueId,
                    ),
                    {
                      uniqueId: uniqueId,

                      anonymousUserId: userIdAnonymous,
                      header: `Visit ${claimVoucherAnonymousTemp.outletNameList[0]} to get your voucher!`,
                      message: `${claimVoucherAnonymousTemp.campaignName} - ${claimVoucherAnonymousTemp.campaignDescription}`,
                      sendTimestamp: Date.now() + milisecondsToAdd,
                      sent: null,

                      taggableVoucherId: claimVoucherAnonymousTemp.uniqueId,

                      createdAt: Date.now(),
                    }
                  );

                  console.log("new doc for voucher created in NotificationAnonymous", uniqueId);
                }
                else
                  console.log("doc found for voucher in NotificationAnonymous", notificationAnonymousSnapshot.docs[0].data().uniqueId);
                break;
              }
            }
          }
        }
      }

      TempStore.update(s => {
        s.rewardsModal = rewardsModalVisiblity;
        s.claimVoucherAnonymous = claimVoucherAnonymousTemp;
      });
    })();
  }, [claimVoucherList, userIdAnonymous, userPhone, userName, anonymousPointsBalance]);

  /////////////////////////////////////////////////////////

  // 2023-09-14 - Determine the available voucher that can claim, for existing user

  useEffect(() => {
    (async () => {
      var rewardsModalVisiblity = false;
      var claimVoucherEUTemp = {};

      if (
        // global.userIdAnonymousLoaded && global.userPhoneLoaded
        global.userPhoneLoaded
        &&
        !global.isClaimedVoucherShownOnce
      ) {
        // proceed when the 2 states is fully loaded

        if (userIdAnonymous !== 'none' && userPhone !== '') {
          // only proceed when the userPhone is empty

          if (userPointsBalance > 0 && claimVoucherList.length > 0) {
            // only proceed when the anonymous user's points balance > 0

            let taggableVouchersSorted = claimVoucherList.slice(0).sort((a, b) =>
              (b.voucherPointsRequired ? b.voucherPointsRequired : 0) - (a.voucherPointsRequired ? a.voucherPointsRequired : 0)
            );

            for (let i = 0; i < taggableVouchersSorted.length; i++) {
              if (taggableVouchersSorted[i].voucherPointsRequired > 0 &&
                taggableVouchersSorted[i].voucherPointsRequired <= userPointsBalance &&
                taggableVouchersSorted[i].voucherQuantity > 0) {
                // means can be claimed/purchase by the current anonymous user

                claimVoucherEUTemp = taggableVouchersSorted[i];
                rewardsModalVisiblity = true;

                const notificationAnonymousSnapshot = await getDocs(
                  query(
                    collection(global.db, Collections.NotificationAnonymous),
                    where('anonymousUserId', '==', userIdAnonymous),
                    where('taggableVoucherId', '==', claimVoucherEUTemp.uniqueId),
                    limit(1),
                  )
                );

                if (notificationAnonymousSnapshot.empty) {
                  // const miliseconds24Hours = 86400000;
                  const miliseconds24Hours = 120000; // 2 mins

                  const uniqueId = uuidv4();
                  await setDoc(
                    doc(
                      global.db, Collections.NotificationAnonymous, uniqueId,
                    ),
                    {
                      uniqueId: uniqueId,

                      anonymousUserId: userIdAnonymous,
                      header: `Visit ${claimVoucherEUTemp.outletNameList[0]} to get your voucher!`,
                      message: `${claimVoucherEUTemp.campaignName} - ${claimVoucherEUTemp.campaignDescription}`,
                      sendTimestamp: Date.now() + miliseconds24Hours,
                      sent: null,

                      taggableVoucherId: claimVoucherEUTemp.uniqueId,

                      createdAt: Date.now(),
                    }
                  );

                  console.log("new doc for voucher created in NotificationAnonymous", uniqueId);
                }
                else
                  console.log("doc found for voucher in NotificationAnonymous", notificationAnonymousSnapshot.docs[0].data().uniqueId);
                break;
              }
            }
          }
        }
      }

      TempStore.update(s => {
        s.rewardsModalEU = rewardsModalVisiblity;
        s.claimVoucherEU = claimVoucherEUTemp;
      });
    })();
  }, [
    claimVoucherList,
    userIdAnonymous,
    userPhone,
    userName,
    userPointsBalance,
  ]);

  /////////////////////////////////////////////////////////

  // 2024-03-01 - For redeem voucher popup flow

  useEffect(() => {
    (async () => {
      if (global.redeemVoucherTimerId) {
        clearTimeout(global.redeemVoucherTimeoutId);
      }

      global.redeemVoucherTimeoutId = setTimeout(() => {
        var redeemVoucherModalTemp = false;
        var redeemVoucherTemp = {};

        if (global.userPhoneLoaded &&
          !global.isRedeemVoucherModalShownOnce) {
          if (userIdAnonymous !== 'none' && userPhone !== '') {
            if (availableUserTaggableVouchers.length > 0) {
              for (let i = 0; i < availableUserTaggableVouchers.length; i++) {
                if (
                  availableUserTaggableVouchers[i].activationDate
                    ?
                    (
                      moment().isAfter(availableUserTaggableVouchers[i].activationDate)
                        ?
                        true
                        :
                        false
                    )
                    :
                    true
                ) {
                  redeemVoucherModalTemp = true;
                  redeemVoucherTemp = availableUserTaggableVouchers[i];
                }
              }
            }
          }
        }

        if (typedVoucherCode) {
          // means the user just key in a voucher code in Cart screen, no need show redeem voucher popup
        }
        else {
          if ((window.location.pathname).includes('/error') || (window.location.pathname).includes('/scan')) {
            return;
          } else {
            TempStore.update(s => {
              s.redeemVoucherModal = redeemVoucherModalTemp;
              s.redeemVoucher = redeemVoucherTemp;
            });
          }
        }
      }, 1000);
    })();
  }, [
    // claimVoucherList,
    userIdAnonymous,
    userPhone,
    // userName,
    userPointsBalance,

    // userTaggableVouchers,
    // taggableVouchers,

    availableUserTaggableVouchers,

    typedVoucherCode,
  ]);

  /////////////////////////////////////////////////////////

  const redirectToLoyaltyCampaignList = async (outletId) => {
    // const outletSnapshot = await firebase.firestore()
    //   .collection(Collections.Outlet)
    //   .where('uniqueId', '==', outletId)
    //   .limit(1)
    //   .get();

    const outletSnapshot = await getDocs(
      query(
        collection(global.db, Collections.Outlet),
        where('uniqueId', '==', outletId),
        limit(1),
      )
    );

    var outlet = null;
    if (!outletSnapshot.empty) {
      outlet = outletSnapshot.docs[0].data();
    }

    CommonStore.update(s => {
      s.selectedOutlet = outlet;
    });

    navigationObj.navigate('LoyaltyCampaignList', {
      // queueResult: userQueue,
      // outletData: userQueue.outletId,
    });
  };

  useEffect(() => {
    var userLoyaltyStampGetLsItemDict = {};

    for (var i = 0; i < userLoyaltyStamps.length; i++) {
      const userLoyaltyStamp = userLoyaltyStamps[i];

      for (var j = 0; j < userLoyaltyStamp.getHistory.length; j++) {
        userLoyaltyStampGetLsItemDict[userLoyaltyStamp.getHistory[j].lsItemId] = true;
      }
    }

    CommonStore.update(s => {
      s.userLoyaltyStampGetLsItemDict = userLoyaltyStampGetLsItemDict;
    });
  }, [userLoyaltyStamps]);

  ///////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////

  // 2023-08-25 - stamps changes

  useEffect(() => {
    var allStackedLoyaltyStampsTemp = [];
    var redeemableStackedLoyaltyStampsTemp = [];

    for (var i = 0; i < selectedOutletLoyaltyStamps.length; i++) {
      if (moment().isSameOrAfter(selectedOutletLoyaltyStamps[i].startDate, 'day')
        && moment().isSameOrBefore(selectedOutletLoyaltyStamps[i].endDate, 'day')) {
        if (selectedOutletLoyaltyStamps[i].lsItems && selectedOutletLoyaltyStamps[i].lsItems.length > 0) {
          const toCheckUserLoyaltyStamp = userLoyaltyStamps.find(findStamp => findStamp.loyaltyStampId === selectedOutletLoyaltyStamps[i].uniqueId);

          if (toCheckUserLoyaltyStamp) {
            // sort the gift items by number of stamps required, from least to most
            let toCheckGiftItems = [...selectedOutletLoyaltyStamps[i].lsItems].sort((a, b) => a.noOfStamp - b.noOfStamp);

            for (var j = 0; j < toCheckGiftItems.length; j++) {
              if (
                toCheckUserLoyaltyStamp.stampCount >= toCheckGiftItems[j].noOfStamp
                &&
                !toCheckUserLoyaltyStamp.getIdHistory.includes(toCheckGiftItems[j].lsItemId)
              ) {
                // means this user loyalty stamp got enough stamp count, and didn't redeem this particular item before

                redeemableStackedLoyaltyStampsTemp.push({
                  ...selectedOutletLoyaltyStamps[i],
                  ...toCheckUserLoyaltyStamp,

                  redeemableItem: {
                    ...toCheckGiftItems[j],

                    loyaltyStampId: selectedOutletLoyaltyStamps[i].uniqueId,
                    itemSku: toCheckGiftItems[j].outletItemSku,
                  },
                });

                break;
              }
            }

            allStackedLoyaltyStampsTemp.push({
              ...selectedOutletLoyaltyStamps[i],
              ...toCheckUserLoyaltyStamp,
            });
          }
        }
      }
    }

    //////////////////////////////////////

    if (toRedeemStackedLoyaltyStamp && toRedeemStackedLoyaltyStamp.loyaltyStampId) {
      if (allStackedLoyaltyStampsTemp.find(findStamp => findStamp.loyaltyStampId === toRedeemStackedLoyaltyStamp.loyaltyStampId) &&
        !redeemableStackedLoyaltyStampsTemp.find(findStamp => findStamp.loyaltyStampId === toRedeemStackedLoyaltyStamp.loyaltyStampId)) {
        // means not existed in the redeemable user loyalty stamps anymore, clear it

        CommonStore.update(s => {
          s.toRedeemStackedLoyaltyStamp = {};
        });
      }
    }

    // if (toRedeemStackedLoyaltyStamp && toRedeemStackedLoyaltyStamp.uniqueId &&
    //   toRedeemStackedLoyaltyStamp.redeemableItem && toRedeemStackedLoyaltyStamp.redeemableItem.lsItemId) {
    //   if (redeemableStackedLoyaltyStampsTemp.find(findStamp => findStamp.redeemableItem.lsItemId === toRedeemStackedLoyaltyStamp.redeemableItem.lsItemId)) {
    //     // means same 
    //   }
    // }

    //////////////////////////////////////

    console.log('======================');
    console.log('[stamp] update');
    console.log('selectedOutletLoyaltyStamps');
    console.log(selectedOutletLoyaltyStamps);
    console.log('userLoyaltyStamps');
    console.log(userLoyaltyStamps);
    console.log('redeemableStackedLoyaltyStampsTemp');
    console.log(redeemableStackedLoyaltyStampsTemp);
    console.log('======================');

    CommonStore.update(s => {
      s.redeemableStackedLoyaltyStamps = redeemableStackedLoyaltyStampsTemp;
    })
  }, [selectedOutletLoyaltyStamps, userLoyaltyStamps]);

  ///////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////

  const selectedTaggableVoucher = CommonStore.useState(s => s.selectedTaggableVoucher);

  useEffect(() => {
    var overrideItemPriceSkuDictTemp = {};
    var fiItemSkuDictTemp = {};
    var amountOffItemSkuDictTemp = {};
    var percentageOffItemSkuDictTemp = {};
    var buy1Free1ItemSkuDictTemp = {};
    var deliveryItemSkuDictTemp = {};
    var takeawayItemSkuDictTemp = {};

    var overrideCategoryPriceNameDictTemp = {};
    var fiCategoryNameDictTemp = {};
    var amountOffCategoryNameDictTemp = {};
    var percentageOffCategoryNameDictTemp = {};
    var buy1Free1CategoryNameDictTemp = {};
    var deliveryCategoryNameDictTemp = {};
    var takeawayCategoryNameDictTemp = {};

    var allMenuVouchersTemp = [];

    if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
      var availableTaggableVouchers = [selectedTaggableVoucher];

      for (var i = 0; i < availableTaggableVouchers.length; i++) {
        if (availableTaggableVouchers[i].applyAllMenu &&
          (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF ||
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF
          )
        ) {
          // allMenuVouchers.push()

          if (availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              allMenuVouchersTemp.push({
                promotionId: availableTaggableVouchers[i].uniqueId,
                percentageOff: criteria.percentageOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                type: PROMOTION_TYPE.TAKE_PERCENTAGE_OFF,
                voucherId: availableTaggableVouchers[i].voucherId ? availableTaggableVouchers[i].voucherId : '',
                uniqueId: availableTaggableVouchers[i].uniqueId ? availableTaggableVouchers[i].uniqueId : '',
              });

              break; // default only 1 criteria
            }
          }
          else if (availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              allMenuVouchersTemp.push({
                promotionId: availableTaggableVouchers[i].uniqueId,
                amountOff: criteria.amountOff,
                maxQuantity: criteria.maxQuantity,
                minQuantity: criteria.minQuantity,

                quantityMin: criteria.quantityMin,
                quantityMax: criteria.quantityMax,
                priceMin: criteria.priceMin,
                priceMax: criteria.priceMax,

                applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,

                type: PROMOTION_TYPE.TAKE_AMOUNT_OFF,
                voucherId: availableTaggableVouchers[i].voucherId ? availableTaggableVouchers[i].voucherId : '',
                uniqueId: availableTaggableVouchers[i].uniqueId ? availableTaggableVouchers[i].uniqueId : '',
              });

              break; // default only 1 criteria
            }
          }
        }
        else {
          if (
            availableTaggableVouchers[i].voucherType ===
            PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  overrideItemPriceSkuDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    overridePrice: criteria.priceBeforeTax,
                    minSpend: availableTaggableVouchers[i].minSpend || 0,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  overrideCategoryPriceNameDictTemp[criteria.variationItemsSku[k]] =
                  {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    overridePrice: criteria.priceBeforeTax,
                    minSpend: availableTaggableVouchers[i].minSpend || 0,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.FREE_ITEM
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                // 2023-07-31 - use voucher id instead

                fiItemSkuDictTemp[availableTaggableVouchers[i].uniqueId] = {
                  promotionId: availableTaggableVouchers[i].uniqueId,

                  variationItemsSku: criteria.variationItemsSku,

                  getQuantity: criteria.getQuantity,

                  // amountOff: criteria.amountOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                };

                // for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                //   fiItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                //     promotionId: availableTaggableVouchers[i].uniqueId,

                //     getQuantity: criteria.getQuantity,

                //     // amountOff: criteria.amountOff,
                //     maxQuantity: criteria.maxQuantity,
                //     minQuantity: criteria.minQuantity,

                //     quantityMin: criteria.quantityMin,
                //     quantityMax: criteria.quantityMax,
                //     priceMin: criteria.priceMin,
                //     priceMax: criteria.priceMax,

                //     applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                //   };
                // }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                // 2023-07-31 - use voucher id instead

                fiCategoryNameDictTemp[availableTaggableVouchers[i].uniqueId] = {
                  promotionId: availableTaggableVouchers[i].uniqueId,

                  variationItemsSku: criteria.variationItemsSku,

                  getQuantity: criteria.getQuantity,

                  // amountOff: criteria.amountOff,
                  maxQuantity: criteria.maxQuantity,
                  minQuantity: criteria.minQuantity,

                  quantityMin: criteria.quantityMin,
                  quantityMax: criteria.quantityMax,
                  priceMin: criteria.priceMin,
                  priceMax: criteria.priceMax,

                  applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                };

                // for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                //   fiCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                //     promotionId: availableTaggableVouchers[i].uniqueId,

                //     getQuantity: criteria.getQuantity,

                //     // amountOff: criteria.amountOff,
                //     maxQuantity: criteria.maxQuantity,
                //     minQuantity: criteria.minQuantity,

                //     quantityMin: criteria.quantityMin,
                //     quantityMax: criteria.quantityMax,
                //     priceMin: criteria.priceMin,
                //     priceMax: criteria.priceMax,

                //     applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                //   };
                // }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.ZUS_BUNDLE
          ) {
            // for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
            //   const criteria = availableTaggableVouchers[i].criteriaList[j];

            //   if (
            //     criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
            //   ) {
            //     for (var k = 0; k < criteria.variationItemsSku.length; k++) {
            //       percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
            //         promotionId: availableTaggableVouchers[i].uniqueId,
            //         percentageOff: criteria.percentageOff,

            //         applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
            //       };
            //     }
            //   } else if (
            //     criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
            //   ) {
            //     for (var k = 0; k < criteria.variationItemsSku.length; k++) {
            //       percentageOffCategoryNameDictTemp[criteria.variationItemsSku[k]] =
            //       {
            //         promotionId: availableTaggableVouchers[i].uniqueId,
            //         percentageOff: criteria.percentageOff,

            //         applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
            //       };
            //     }
            //   }
            // }
          } else if (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKE_AMOUNT_OFF
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    amountOff: criteria.amountOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  amountOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    amountOff: criteria.amountOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType ===
            PROMOTION_TYPE.TAKE_PERCENTAGE_OFF
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    percentageOff: criteria.percentageOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  percentageOffCategoryNameDictTemp[criteria.variationItemsSku[k]] =
                  {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    percentageOff: criteria.percentageOff,
                    maxQuantity: criteria.maxQuantity,
                    minQuantity: criteria.minQuantity,

                    quantityMin: criteria.quantityMin,
                    quantityMax: criteria.quantityMax,
                    priceMin: criteria.priceMin,
                    priceMax: criteria.priceMax,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType ===
            PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.buyVariation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
                  buy1Free1ItemSkuDictTemp[criteria.buyVariationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    buyAmount: criteria.buyAmount,
                    getAmount: criteria.getAmount,
                    getPrice: criteria.getPrice,
                    getVariation: criteria.getVariation,
                    getVariationItems: criteria.getVariationItems,
                    getVariationItemsSku: criteria.getVariationItemsSku,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.buyVariation ===
                PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.buyVariationItemsSku.length; k++) {
                  buy1Free1CategoryNameDictTemp[criteria.buyVariationItemsSku[k]] =
                  {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    buyAmount: criteria.buyAmount,
                    getAmount: criteria.getAmount,
                    getPrice: criteria.getPrice,
                    getVariation: criteria.getVariation,
                    getVariationItems: criteria.getVariationItems,
                    getVariationItemsSku: criteria.getVariationItemsSku,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.DELIVERY
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  deliveryItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                    deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                    deliveryDiscountAboveAmount:
                      criteria.deliveryDiscountAboveAmount,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  deliveryCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                    deliveryDiscountAmount: criteria.deliveryDiscountAmount,
                    deliveryDiscountAboveAmount:
                      criteria.deliveryDiscountAboveAmount,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          } else if (
            availableTaggableVouchers[i].voucherType === PROMOTION_TYPE.TAKEAWAY
          ) {
            for (var j = 0; j < availableTaggableVouchers[i].criteriaList.length; j++) {
              const criteria = availableTaggableVouchers[i].criteriaList[j];

              if (
                criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  takeawayItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    takeawayFreeFlag: criteria.takeawayFreeFlag,
                    takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                    takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                    takeawayDiscountAboveAmount:
                      criteria.takeawayDiscountAboveAmount,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              } else if (
                criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
              ) {
                for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                  takeawayCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                    promotionId: availableTaggableVouchers[i].uniqueId,
                    takeawayFreeFlag: criteria.takeawayFreeFlag,
                    takeawayFreeAboveAmount: criteria.takeawayFreeAboveAmount,
                    takeawayDiscountAmount: criteria.takeawayDiscountAmount,
                    takeawayDiscountAboveAmount:
                      criteria.takeawayDiscountAboveAmount,

                    applyDiscountPer: availableTaggableVouchers[i].applyDiscountPer || APPLY_DISCOUNT_PER.ORDER,
                  };
                }
              }
            }
          }
        }
      }
    }

    CommonStore.update(s => {
      s.overrideItemPriceSkuDictLC = overrideItemPriceSkuDictTemp;
      s.fiItemSkuDictLC = fiItemSkuDictTemp;
      s.amountOffItemSkuDictLC = amountOffItemSkuDictTemp;
      s.percentageOffItemSkuDictLC = percentageOffItemSkuDictTemp;
      s.buy1Free1ItemSkuDictLC = buy1Free1ItemSkuDictTemp;
      s.deliveryItemSkuDictLC = deliveryItemSkuDictTemp;
      s.takeawayItemSkuDictLC = takeawayItemSkuDictTemp;

      s.overrideCategoryPriceNameDictLC = overrideCategoryPriceNameDictTemp;
      s.fiCategoryNameDictLC = fiCategoryNameDictTemp;
      s.amountOffCategoryNameDictLC = amountOffCategoryNameDictTemp;
      s.percentageOffCategoryNameDictLC = percentageOffCategoryNameDictTemp;
      s.buy1Free1CategoryNameDictLC = buy1Free1CategoryNameDictTemp;
      s.deliveryCategoryNameDictLC = deliveryCategoryNameDictTemp;
      s.takeawayCategoryNameDictLC = takeawayCategoryNameDictTemp;

      s.allMenuVouchers = allMenuVouchersTemp;
    });
  }, [
    // availableLoyaltyCampaigns, 
    selectedTaggableVoucher,
  ]);

  ////////////////////////////////////////////////

  useEffect(() => {
    if (cartItems.length > 0 || cartItemsProcessed.length > 0) {
      setCartIcon(true);
    }
    else {
      setCartIcon(false);
    }
  }, [cartItems, cartItemsProcessed]);

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.uniqueId && userIdAnonymous) {
      global.outletName = selectedOutlet.name;
      global.merchantId = selectedOutlet.merchantId;
      global.outletId = selectedOutlet.uniqueId;
      global.userName = userName;
      global.userIdAnonymous = userIdAnonymous;
    }
  }, [selectedOutlet, userName, userIdAnonymous]);

  ///////////////////////////////////////////////////////////////////

  const checkOutletShiftAndOperatingHours = async () => {
    let isPossibleIssue = false;

    if (currOutletShiftStatus === OUTLET_SHIFT_STATUS.CLOSED) {
      const rev_order_date = moment().format("DD/MM/YYYY");

      const newRev = moment();

      try {
        const currDay = mondayFirst(moment(newRev).day());

        var isOff = false;

        var outletOpeningOff = selectedOutlet.outletOpeningOff;
        if (outletOpeningOff && outletOpeningOff[WEEK[currDay]]) {
          isOff = true;
        }

        var outletOpeningToday = null;

        var startTimeStr = null;
        var endTimeStr = null;

        const outletOpening =
          outletsOpeningDict[selectedOutlet.uniqueId];

        if (outletOpening) {
          outletOpeningToday = outletOpening[WEEK[currDay]];
        }

        if (outletOpeningToday) {
          startTimeStr = outletOpeningToday.split("-")[0];
          endTimeStr = outletOpeningToday.split("-")[1];

          // const startTime = moment(startTimeStr, 'HHmm');
          // const endTime = moment(endTimeStr, 'HHmm');

          var startDateTime = moment(
            `${rev_order_date} ${startTimeStr}`,
            "DD/MM/YYYY HHmm"
          );
          var endDateTime = moment(
            `${rev_order_date} ${endTimeStr}`,
            "DD/MM/YYYY HHmm"
          );

          global.openingStartDateTime = startDateTime;
          global.openingEndDateTime = endDateTime;

          // if (moment(endDateTime).hours() === 0 &&
          //   moment(endDateTime).minutes() === 0 &&
          //   moment(endDateTime).seconds() === 0) {
          //   endDateTime = moment(endDateTime).set({
          //     hour: 23,
          //     minute: 59,
          //     second: 59,
          //   });
          // }

          // console.log(moment(newRev).format());
          // console.log(moment(startDateTime).format());
          // console.log(moment(endDateTime).format());
          // console.log(
          //   moment(newRev).isSameOrAfter(startDateTime)
          // );
          // console.log(moment(newRev).isBefore(endDateTime));

          // isValidOrderTime =
          //   moment(newRev).isSameOrAfter(startDateTime) &&
          //   moment(newRev).isBefore(endDateTime) &&
          //   !isOff;

          if (global.openingStartDateTime === global.openingEndDateTime) {
            // if same, likely got issue, try to retrieve the relevant data first

            isPossibleIssue = true;
          }
        }
      } catch (ex) {
        console.error(ex);
      }
    }

    if (!isPossibleIssue) {
      let outletsOpeningDictTemp = {};
      let currOutletShiftTemp = {};
      let currOutletShiftStatusTemp = OUTLET_SHIFT_STATUS.CLOSED;

      const outletOpeningSnapshot = await getDocs(
        query(
          collection(global.db, Collections.OutletOpening),
          where('outletId', '==', selectedOutlet.uniqueId),
        )
      );

      if (outletOpeningSnapshot && !outletOpeningSnapshot.empty) {
        // var outletsOpeningDictTemp = {};

        for (var i = 0; i < outletOpeningSnapshot.size; i++) {
          const record = outletOpeningSnapshot.docs[i].data();

          outletsOpeningDictTemp[record.outletId] = record;
        }

        CommonStore.update(s => {
          s.outletsOpeningDict = outletsOpeningDictTemp;
        });
      }

      const outletShiftSnapshot = await getDocs(
        query(
          collection(global.db, Collections.OutletShift),
          where('outletId', '==', selectedOutlet.uniqueId),
          orderBy('createdAt', 'desc'),
          limit(1),
        )
      );

      if (outletShiftSnapshot && !outletShiftSnapshot.empty) {
        // var currOutletShiftTemp = {};
        // var currOutletShiftStatusTemp = OUTLET_SHIFT_STATUS.CLOSED;

        if (outletShiftSnapshot && !outletShiftSnapshot.empty) {
          currOutletShiftTemp = outletShiftSnapshot.docs[0].data();

          if (currOutletShiftTemp.closeDate === null) {
            currOutletShiftStatusTemp = OUTLET_SHIFT_STATUS.OPENED;
          } else {
            currOutletShiftStatusTemp = OUTLET_SHIFT_STATUS.CLOSED;
          }
        }

        if (outletShiftSnapshot) {
          CommonStore.update((s) => {
            s.currOutletShift = currOutletShiftTemp;
            s.currOutletShiftStatus = currOutletShiftStatusTemp;
          });
        }
      }
    }
  };

  useEffect(() => {
    if (selectedOutlet !== null) {
      if (global.checkOutletShiftAndOperatingHoursTimerId) {
        clearTimeout(global.checkOutletShiftAndOperatingHoursTimerId);
      }

      if (global.checkOutletShiftAndOperatingHoursTimerTriggerTimes < 10) {
        // stop checking after 10 times

        global.checkOutletShiftAndOperatingHoursTimerId = setTimeout(() => {
          global.checkOutletShiftAndOperatingHoursTimerTriggerTimes++;

          checkOutletShiftAndOperatingHours();
        }, 10000);
      }
    }
  }, [
    selectedOutlet,
    currOutletShiftStatus,
    outletsOpeningDict,
  ]);

  ///////////////////////////////////////////////////////////////////

  useEffect(() => {
    if (selectedOutlet && selectedOutlet.uniqueId) {
      global.selectedOutlet = selectedOutlet;
    }
  }, [
    selectedOutlet?.updatedAt,
  ]);

  ///////////////////////////////////////////////////////////////////

  const onCartClicked = async () => {
    if (cartItems.length > 0 || cartItemsProcessed.length > 0) {
      // navigationObj && navigationObj.navigate("Cart", { test: null, outletData: selectedOutlet });

      const subdomain = await AsyncStorage.getItem('latestSubdomain');

      // var isNeededToPushStateFirst = false;
      // if (global.currPageStack[global.currPageStack.length - 1] === 'CartScreen') {
      //   isNeededToPushStateFirst = true;
      // }

      // if (isNeededToPushStateFirst) {
      //   window.history.pushState({
      //     page: 'cartAfterPaidOnline',
      //   }, '');
      // }

      global.clickedCartIcon = true;

      if (!subdomain) {
        global.linkToFunc && global.linkToFunc(`${prefix}/outlet/cart`);

        CommonStore.update(s => {
          s.currPageIframe = 'Cart';
        });
      }
      else {
        // if (subdomain === 'hominsan-ss15' || subdomain === 'hominsanttdi') {
        if (true) {
          if (
            (global.upsellingCampaignsAfterCheckout && global.upsellingCampaignsAfterCheckout.length > 0)
            ||
            (global.upsellingCampaignsAfterCheckoutRecommendation && global.upsellingCampaignsAfterCheckoutRecommendation.length > 0)
          ) {
            CommonStore.update((s) => {
              s.currPage = "UpsellCart";

              s.currPageIframe = 'UpsellingAfterCheckout';
            });

            global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/upsell-cart`);
          }
          else {
            global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);

            CommonStore.update(s => {
              s.currPageIframe = 'Cart';
            });
          }
        }
        else {
          global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/cart`);
        }
      }
    } else {
      CommonStore.update(s => {
        s.alertObj = {
          title: 'Info',
          message: 'No item in your cart at the moment',
        };

        // s.isAuthenticating = false;
      });

      // Alert.alert("Info", "No item in your cart at the moment", [
      //   { text: "OK", onPress: () => { } }
      // ],
      //   { cancelable: false })      
    }
  };

  const onUserHelpClicked = async () => {
    // here can send the info
  };

  const updateUserCartGeneric = async (json, outlet, firebaseUid) => {
    const body = {
      userId: firebaseUid,
      // outletId: outlet.uniqueId,
      outletId: json.outletId,
      tableId: json.tableId,
      tableCode: json.tableCode,
      // tablePax: json.tablePax ? json.tablePax : seatingPax,
      tablePax: json.tablePax ? json.tablePax : 1,
      cartItems: [],

      waiterId: json.waiterId ? json.waiterId : '',
    };

    // global.selectedOutlet = qrGenericOutlet;

    CommonStore.update(s => {
      // s.selectedOutlet = qrGenericOutlet;

      s.scannedQrData = json;

      s.orderType = ORDER_TYPE.DINEIN;

      s.selectedOutletTableId = json.tableId;
      s.selectedOutletWaiterId = json.waiterId;
      s.selectedOutletTablePax = json.tablePax;
      s.selectedOutletTableCode = json.tableCode;

      s.timestampOutletCategory = Date.now();

      // 2022-10-08 - Reset cart items
      // s.cartItems = [];
      // s.cartItemsProcessed = [];
      // s.cartOutletId = outlet.uniqueId;

      // s.selectedOutletItem = {};
      // s.selectedOutletItemAddOn = {};
      // s.selectedOutletItemAddOnChoice = {};
      // s.onUpdatingCartItem = null;

      // s.isLoading = false;

      // s.molpayResult = null;
    }, async () => {
      // setShowGenericQRModal(false);

      // TempStore.update(s => {
      //   s.showGreetingPopup = false;
      // });

      // if (qrGenericOutlet && qrGenericOutlet.uniqueId) {
      //   // navigation.navigate('OutletMenu', { 
      //   //     outletData: outlet, 
      //   //     orderType: 0, 
      //   //     test: 1 
      //   // });

      //   // const subdomain = await AsyncStorage.getItem('latestSubdomain');
      //   const subdomain = global.subdomainOnly;

      //   if (!subdomain) {
      //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/menu`);
      //   }
      //   else {
      //     global.linkToFunc && global.linkToFunc(`${prefix}/outlet/${subdomain}/menu`);
      //   }

      //   // linkTo && linkTo(`${prefix}/outlet/menu`);
      // }

      // if (scanner && scanner.current) {
      //     scanner.current.reactivate();
      // }
    });
  };

  {/* 20240618 e-invoice */ }
  const registerUser = async () => {
    // 2023-05-06 - No need verify first
    // if (!isVerified) {
    //     // CommonStore.update((s) => {
    //     //     s.alertObj = {
    //     //         title: "Error",
    //     //         message: "Please verify your phone number first.",
    //     //     };
    //     // });
    //     window.confirm(
    //         `Please verify your phone number first.`
    //     );
    //     return;
    // }

    var userNumber = epPhoneToTemp.replaceAll('-', '').replaceAll(' ', '').replaceAll('+', '');

    // if (userPhone.startsWith('6')) {
    //     userPhone = userPhone.slice(1);
    // }

    if (!userNumber.startsWith('6')) {
      userNumber = '6' + userNumber;
    }

    if (userNumber && epNameToTemp) {

    }
    else {
      window.confirm('Please fill in the name and phone number before proceed.');

      return;
    }

    if (userNumber && !(userNumber.length === 10 || userNumber.length === 11)) {
      if (
        (userNumber.startsWith('011') && userNumber.length === 11)
        ||
        (userNumber.startsWith('6011') && userNumber.length === 12)
      ) {
        // still valid, do nothing
      }
      else {
        window.confirm(
          "Invalid phone number format.\neg: 60123456789."
        );
        return;
      }
    }

    let epPhoneToTempParsed = epPhoneToTemp;
    if (!epPhoneToTempParsed.startsWith('6')) {
      epPhoneToTempParsed = `6${epPhoneToTempParsed}`;
    }

    let epPhoneToParsed = epPhoneTo;
    if (!epPhoneToTempParsed.startsWith('6')) {
      epPhoneToParsed = `6${epPhoneToParsed}`;
    }

    let toUpdateEiDetails = false;
    if (
      epStateToTemp !== epStateTo ||
      epNameToTemp !== epNameTo ||
      // epPhoneToTempParsed !== epPhoneToParsed ||
      userNumber !== epPhoneToParsed ||
      epAddr1ToTemp !== epAddr1To ||
      epCityToTemp !== epCityTo ||
      epCodeToTemp !== epCodeTo ||
      epIdTypeToTemp !== epIdTypeTo ||
      epIdToTemp !== epIdTo ||
      epTinToTemp !== epTinTo ||
      epEmailToTemp !== epEmailTo
    ) {
      toUpdateEiDetails = true;
    }
    // if (showVoucherInfo && showVoucherInfo.voucherQuantity > 0) {

    // }
    // else {
    //     window.confirm('This voucher is already fully redeemed.');

    //     return;
    // }

    // var userPhone = phone.replaceAll('-', '');

    // if (userPhone.startsWith('6')) {
    //     userPhone = userPhone.slice(1);
    // }

    //////////////////////////////////////////////////

    // 2023-12-04 - to check for google submission flow

    let isValidToProceed = true;
    let toOpenGoogleReviewTabAfterClosed = false;
    let toUpdateGR = false;

    if (!submittedGoogleReview && isMobile()) {
      // means this user haven't proceed to submit a google review

      if (!gReview && selectedOutlet && selectedOutlet.placeUrl && (
        rating > 0
        ||
        review.length > 0 // if filled in something already, force the user to fill all
      )) {
        // means this crm user havent submitted google review before (after saved to db)

        if (rating <= 0) {
          window.confirm(
            "The minimum rating is 1 star."
          );
          return;
        }
        else if (review.length < 10) {
          window.confirm(
            "The minimum length of the review is 10 characters."
          );
          return;
        }

        // all ok, open the link

        // isValidToProceed = false; // set to false, so that don't proceed to registered the actual operation first

        // setShowGRIframe(true);

        if (rating >= 4) {
          // 4 stars and above

          toOpenGoogleReviewTabAfterClosed = true;

          navigator.clipboard.writeText(review);

          Toastify({
            text: `Review copied to clipboard!`,
            duration: 3000,
            // destination: "https://github.com/apvarun/toastify-js",
            newWindow: true,
            close: false,
            gravity: "top", // `top` or `bottom`
            position: "right", // `left`, `center` or `right`
            stopOnFocus: true, // Prevents dismissing of toast on hover
            style: {
              background: "linear-gradient(to right, #4E9F7D, #75bd9f)",
              color: 'white',

              // marginLeft: '15px !important',
              // marginRight: '15px !important',
            },
            onClick: function () { } // Callback after click
          }).showToast();
        }

        toUpdateGR = true;
      }
      else {
        // this user already done in the past
      }
    }

    //////////////////////////////////////////////////

    if (
      isValidToProceed
      // false
    ) {
      const body = {
        userPhone: userNumber,
        outletId: selectedOutlet.uniqueId,
        merchantId: selectedOutlet.merchantId,
        merchantName: selectedOutlet.name,
        outletName: selectedOutlet.outletName || selectedOutlet.outletName,
        merchantLogo: selectedOutlet.merchantLogo || '',
        outletCover: selectedOutlet.outletCover || '',
        userName: epNameToTemp ? epNameToTemp : userName,

        dob: moment(birthday).valueOf(),

        address: address,
        lat: lat,
        lng: lng,

        // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
        taggableVoucherId: (showVoucherInfo && showVoucherInfo.uniqueId) ? showVoucherInfo.uniqueId : null,

        isVerified: isVerified,

        userIdAnonymous: userIdAnonymous,
        toConvertAnonymousPoints: true,

        //////////////////////

        // google review

        rating: rating,
        review: review,
        toUpdateGR: toUpdateGR,
        outletEmail: selectedOutlet.email,

        //////////////////////

        // 2024-02-29 - loyalty voucher support

        loyaltyCampaignId: (showVoucherInfo && showVoucherInfo.loyaltyCampaignId) ? (showVoucherInfo.loyaltyCampaignId) : '',
        batchId: (showVoucherInfo && showVoucherInfo.batchId) ? (showVoucherInfo.batchId) : '',
        batchIndex: (showVoucherInfo && showVoucherInfo.batchIndex) ? (showVoucherInfo.batchIndex) : '',

        //////////////////////  

        // 2024-06-18 e-invoice
        toUpdateEiDetails: toUpdateEiDetails ? toUpdateEiDetails : false,
        epNameTo: epNameToTemp ? epNameToTemp : epNameTo,
        // epPhoneTo: epPhoneToTemp ? epPhoneToTemp : epPhoneTo,
        epPhoneTo: userNumber,
        epAddr1To: epAddr1ToTemp ? epAddr1ToTemp : epAddr1To,
        epCityTo: epCityToTemp ? epCityToTemp : epCityTo,
        epCodeTo: epCodeToTemp ? epCodeToTemp : epCodeTo,
        epStateTo: epStateToTemp ? epStateToTemp : epStateTo,
        emailSecond: epEmailToTemp ? epEmailToTemp : epEmailTo,
        tin: epTinToTemp ? epTinToTemp : epTinTo,
        eiIdType: epIdTypeToTemp ? epIdTypeToTemp : epIdTypeTo,
        eiId: epIdToTemp ? epIdToTemp : epIdTo,

        tracking: '1',
      };

      CommonStore.update(s => {
        s.isLoading = true;
      });

      // const userSnapshot = await firebase.firestore()
      //     .collection(Collections.User)
      //     // .where('uniqueName', '==', uniqueName)
      //     .where('number', '==', userPhone)
      //     .limit(1)
      //     .get();

      // var findUser = null;
      // if (!userSnapshot.empty) {
      //     findUser = userSnapshot.docs[0].data();
      // }

      if (
        // !findUser
        true
      ) {
        // let token = await AsyncStorage.getItem('accessToken');
        // if (!token) {
        //     token = global.accessToken;
        // }

        // fetch(`${apiUrl + API.claimTaggableVoucherKweb}`, {
        //     method: "POST",
        //     body: JSON.stringify(body),
        //     headers: {
        //         "Content-type": "application/json; charset=UTF-8",
        //         'Authorization': `Bearer ${token}`,
        //     },
        // }).then(async (result) => {
        ApiClient.POST(API.claimTaggableVoucherKweb, body).then(async (result) => {
          ////////////////////////////////////////////////////////////////////////////////

          setTimeout(async () => {
            // write in the user name and phone first

            await AsyncStorage.setItem('storedUserName', epNameToTemp);
            await AsyncStorage.setItem('storedUserPhone', userNumber);

            global.storedUserName = epNameToTemp;
            global.storedUserPhone = userNumber;

            await signInWithPhoneForCRMUser(selectedOutlet);
          }, 100);

          ////////////////////////////////////////////////////////////////////////////////

          if (global.voucherScreenshotUrl && global.voucherScreenshotName) {
            saveAs(global.voucherScreenshotUrl, global.voucherScreenshotName);
          }

          ////////////////////////////////////////////////////////////////////////////////

          // 2024-03-05 - screenshot voucher, while waiting for api to completed

          // try {
          //     const screenshotContainer = document.getElementById('screenshotContainer');

          //     if (screenshotContainer) {
          //         // Use html2canvas to capture the container element
          //         html2canvas(screenshotContainer, {
          //             width: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
          //             height: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
          //             windowWidth: Platform.OS === 'ios' ? (windowWidth + windowWidth * 0.3) : windowWidth,
          //             windowHeight: Platform.OS === 'ios' ? (windowHeight + windowHeight * 0.3) : windowHeight,
          //             x: Platform.OS === 'ios' ? (windowWidth * 0.15) : 0,
          //             y: Platform.OS === 'ios' ? (windowHeight * 0.15) : 0,
          //             scrollY: windowHeight * 0.3,
          //             scrollX: 0,
          //             allowTaint: true,
          //             letterRendering: 1,
          //             useCORS: true,
          //         }).then(canvas => {
          //             // Convert the canvas to a data URI
          //             // const screenshotUrl = canvas.toDataURL('image/png').replace("image/png", "image/octet-stream");
          //             const screenshotUrl = canvas.toDataURL('image/png');

          //             // Create a temporary link element
          //             const link = document.createElement('a');
          //             link.target = '_blank';
          //             link.href = screenshotUrl;
          //             link.download = `${selectedOutlet.name}-${showVoucherInfo.campaignName}-${moment(showVoucherInfo.promoDateStart).format('DD MMM YY')}-${moment(showVoucherInfo.promoDateEnd).format('DD MMM YY')}.png`;

          //             // Trigger the download
          //             document.body.appendChild(link);
          //             link.click();

          //             // Clean up
          //             document.body.removeChild(link);
          //         });
          //     }
          // }
          // catch (ex) {
          //     console.error(ex);
          // }

          ////////////////////////////////////////////////////////////////////////////////

          if (result && result.status === "success") {
            // here can start proceed to next page, to show cart modal page (if applicable)                    

            // CommonStore.update(s => {
            //     s.registerUserOrder = result.userOrder || {};
            // });

            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Success",
            //         message: "Account creation success! Please check your phone for the password.",
            //     };                        

            //     // s.isAuthenticating = false;
            // });  

            if (showVoucherInfo) {
              // window.confirm(
              //     `${result.message}`
              // );

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Info",
                  message: `${result.message}`,
                };
              });
            }
            else if (showVoucherPromotionInterestedInfo) {
              // window.confirm(
              //     `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`
              // );

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Info",
                  message: `Subscribed successfully! We will update you if there are any upcoming promotions or vouchers.`,
                };
              });
            }

            ///////////////////////////////////////////////////////////

            var userCrm = result.userCrm;
            var userActual = result.userActual;
            var userTaggableVoucher = result.data;
            // var taggableVoucher = result.taggableVoucher;

            ///////////////////////////////////////////////////////////

            // determine the next flow:
            // a. check if is voucher modal, and if claimed, if not claimed?
            // b. check if is promo interested modal, can just 

            ///////////////////////////////////////////////////////////

            updateWebTokenAnonymous();

            ///////////////////////////////////////////////////////////

            // if (showVoucherInfo) {
            //   if (userCrm && userActual && userTaggableVoucher) {
            //     // can proceed to next page (cart info)

            //     TempStore.update(s => {
            //       s.userCrm = userCrm;
            //       s.userActual = userActual;
            //       s.userTaggableVoucher = userTaggableVoucher;
            //     });

            //     setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.CART_INFO);

            //     // now should showing a spinner ex: checking if is applicable voucher...
            //   }
            //   else {
            //     TempStore.update(s => {
            //       s.showVoucherInfo = null;
            //       s.showVoucherPromotionInterestedInfo = false;
            //       s.isPaidFirstOrder = false;

            //       s.cartItemsT = [];
            //       s.cartItemsProcessedT = [];

            //       s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;

            //       s.claimableVoucher = null;
            //     });

            //     setShowVoucherPromotionInfoModal(false);
            //     setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

            //     clearInterval(cooldownTimer);
            //     cooldownTimerTime = 10;
            //     setCooldownTimerTimeState(cooldownTimerTime);
            //     setCooldownActive(true);
            //   }
            // }
            // else if (showVoucherPromotionInterestedInfo) {
            //   TempStore.update(s => {
            //     s.showVoucherInfo = null;
            //     s.showVoucherPromotionInterestedInfo = false;
            //     s.isPaidFirstOrder = false;

            //     s.cartItemsT = [];
            //     s.cartItemsProcessedT = [];

            //     s.cartOutletIdT = (selectedOutlet && selectedOutlet.uniqueId) ? selectedOutlet.uniqueId : null;

            //     s.claimableVoucher = null;
            //   });

            //   setShowVoucherPromotionInfoModal(false);
            //   setCurrVoucherPromotionInfoPage(VOUCHER_PROMOTION_INFO_PAGE.REGISTER_USER);

            //   clearInterval(cooldownTimer);
            //   cooldownTimerTime = 10;
            //   setCooldownTimerTimeState(cooldownTimerTime);
            //   setCooldownActive(true);
            // }

            TempStore.update((s) => {
              s.showSignUpMember = false;
            })

            // hide first, block code execution
            // alert('Success, Register successfully')
          }
          else {
            // CommonStore.update((s) => {
            //     s.alertObj = {
            //         title: "Error",
            //         message: "Failed to create the account.",
            //     };
            // });

            if (showVoucherInfo) {
              // window.confirm(
              //     `Unable to claim the voucher for now.`
              // );

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Info",
                  message: `Unable to claim the voucher for now, please try again later.`,
                };
              });
            }
            else if (showVoucherPromotionInterestedInfo) {
              // window.confirm(
              //     `Unable to subscribe to the outlet for now.`
              // );

              CommonStore.update((s) => {
                s.alertObj = {
                  title: "Info",
                  message: `Unable to subscribe to the outlet for now, please try again later.`,
                };
              });
            }
          }

          CommonStore.update(s => {
            s.isLoading = false;
          });

          // if (global.createUserOrderBody) {
          //   // append info

          //   if (!userPhone.startsWith('6')) {
          //     userPhone = `6${userPhone}`;
          //   }

          //   global.createUserOrderBody.userName = name;
          //   global.createUserOrderBody.userPhone = userPhone;

          //   await AsyncStorage.setItem('storedUserName', name);
          //   await AsyncStorage.setItem('storedUserPhone', userPhone);

          //   global.storedUserName = name;
          //   global.storedUserPhone = userPhone;
          // }

          // if (global.createUserOrderBody) {
          //   // means still existed, help to create order

          //   var createUserOrderBodyLocal = {
          //     ...global.createUserOrderBody,
          //   };

          //   global.createUserOrderBody = null;

          //   ApiClient.POST(API.createUserOrder, createUserOrderBodyLocal, {
          //     timeout: 100000,
          //   }).then(async (result) => {
          //     if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
          //       global.takeawayOrders.push(result);
          //     }

          //     CommonStore.update(s => {
          //       // 2022-10-08 - Clear cart items
          //       s.cartItems = [];
          //       s.cartItemsProcessed = [];

          //       s.selectedOutletItem = {};
          //       s.selectedOutletItemAddOn = {};
          //       s.selectedOutletItemAddOnChoice = {};
          //       s.onUpdatingCartItem = null;
          //     });

          //     const latestSubdomain = await AsyncStorage.getItem(
          //       "latestSubdomain"
          //     );

          //     if (latestSubdomain) {
          //       if (createUserOrderBodyLocal.orderType === ORDER_TYPE.DINEIN) {
          //         linkToFunc &&
          //           linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history`);
          //       }
          //       else if (createUserOrderBodyLocal.orderType === ORDER_TYPE.PICKUP) {
          //         linkToFunc &&
          //           linkToFunc(`${prefix}/outlet/${latestSubdomain}/order-history-t`);
          //       }
          //     }
          //   });

          //   TempStore.update(s => {
          //     s.showVoucherInfo = null;
          //     s.showVoucherPromotionInterestedInfo = false;
          //     s.isPaidFirstOrder = false;

          //     s.claimableVoucher = null;
          //   });

          //   clearInterval(cooldownTimer);
          //   cooldownTimerTime = 10;
          //   setCooldownTimerTimeState(cooldownTimerTime);
          //   setCooldownActive(true);

          //   // await deleteUserCart();

          //   CommonStore.update(s => {
          //     s.timestampOutletCategory = Date.now();
          //   });

          //   // open webpage

          //   console.log('open webpage (1)');

          //   if (toOpenGoogleReviewTabAfterClosed &&
          //     selectedOutlet && selectedOutlet.placeUrl) {
          //     if (selectedOutlet.GRPopup) {
          //       window.open(selectedOutlet.placeUrl, '_blank');
          //     }
          //     else {
          //       window.location.replace(selectedOutlet.placeUrl);
          //     }
          //   }
          // }
          // else {
          //   // open webpage

          //   console.log('open webpage (2)');

          //   if (toOpenGoogleReviewTabAfterClosed &&
          //     selectedOutlet && selectedOutlet.placeUrl) {
          //     if (selectedOutlet.GRPopup) {
          //       window.open(selectedOutlet.placeUrl, '_blank');
          //     }
          //     else {
          //       window.location.replace(selectedOutlet.placeUrl);
          //     }
          //   }
          // }
        });
      }
      else {
        // CommonStore.update(s => {
        //     s.isLoading = false;
        // });

        // setExistingCustomer(true);
      }
    }
  };

  return (
    <View style={{
      width: isMobile() ? windowWidth : windowWidth,
      height: windowHeight,
      alignSelf: 'center',
      justifyContent: 'center'
    }}>
      <style type="text/css">{`
        @font-face {
          font-family: 'FontAwesome';
          src: url(${FontAwesomeTTF}) format('truetype');
        }

        @font-face {
          font-family: 'SimpleLineIcons';
          src: url(${SimpleLineIconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Entypo';
          src: url(${EntypoTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Ionicons';
          src: url(${IoniconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'Feather';
          src: url(${FeatherTTF}) format('truetype');
        }

        @font-face {
          font-family: 'AntDesign';
          src: url(${AntDesignTTF}) format('truetype');
        }

        @font-face {
          font-family: 'MaterialCommunityIcons';
          src: url(${MaterialCommunityIconsTTF}) format('truetype');
        }

        @font-face {
          font-family: 'MaterialIcons';
          src: url(${MaterialIconsTTF}) format('truetype');
        }


        @font-face {
          font-family: 'NunitoSans-Bold';
          src: url(${NunitoSansBoldTTF}) format('truetype');
        }

        @font-face {
          font-family: 'NunitoSans-SemiBold';
          src: url(${NunitoSansSemiBoldTTF}) format('truetype');
        }

        @font-face {
          font-family: 'NunitoSans-Regular';
          src: url(${NunitoSansRegularTTF}) format('truetype');
        }
      `}</style>

      <AwesomeAlert
        show={alertObj !== null}
        showProgress={false}
        title={alertObj ? alertObj.title : ''}
        message={alertObj ? alertObj.message : ''}
        closeOnTouchOutside={true}
        closeOnHardwareBackPress={false}
        showCancelButton={false}
        showConfirmButton={true}
        // cancelText="No, cancel"
        confirmText="OK"
        confirmButtonColor="#DD6B55"
        onCancelPressed={() => {
          // this.hideAlert();
          // setShowAlertLogin(false)
          CommonStore.update(s => {
            s.alertObj = null;
          });
        }}
        onConfirmPressed={() => {
          // this.hideAlert();
          // setShowAlertLogin(false)
          if (alertObj && typeof alertObj.onConfirmPressed === 'function') {
            alertObj.onConfirmPressed();
          }

          CommonStore.update(s => {
            s.alertObj = null;
          });
        }}
        alertContainerStyle={{
          zIndex: 100,
        }}
      />

      <AwesomeAlerteI
        show={alertObjeI !== null}
        showProgress={false}
        title={alertObjeI ? alertObjeI.title : ''}
        message={alertObjeI ? alertObjeI.message : ''}
        closeOnTouchOutside={true}
        closeOnHardwareBackPress={false}
        showConfirmButton={true}
        showCancelButton={true}
        confirmText="YES"
        cancelText="NO"
        confirmButtonColor="#4E9F7D"
        confirmButtonTextStyle={{ color: 'white' }}
        cancelButtonTextStyle={{ color: 'white' }}
        cancelButtonColor="#DD6B55"
        onCancelPressed={() => {
          CommonStore.update(s => {
            s.emailEInvoice = false;
            s.alertObjeI = null;
          });
        }}
        onConfirmPressed={() => {
          CommonStore.update(s => {
            s.emailEInvoice = true;
            s.alertObjeI = null;
          });
        }}
        alertContainerStyle={{
          zIndex: 100,
        }}
      />

      <AppNavigator />

      {<ShowGreetingPopupModal />}

      {(showVoucherInfo && alertObjeI === null) || (showVoucherPromotionInterestedInfo && alertObjeI === null) ?
        <VoucherPromotionInfo linkToFunc={linkToFunc} />
        : null}

      <GeneralAskUserInfo linkToFunc={linkToFunc} />

      {
        (
          cartIcon
          && (
            (
              currPage !== 'Cart' &&
              currPage !== 'UpsellMenu' &&
              currPage !== 'UpsellCart' &&
              currPage !== 'Reservation' &&
              currPage !== 'ReservationDetails' &&
              currPage !== 'ReservationSummary'
            )
            ||
            window.location.href.includes('/menu')
          )
        ) ?
          // <Draggable
          //   shouldReverse={false}
          //   renderSize={100}
          //   // renderColor={Colors.secondaryColor}
          //   isCircle
          //   x={windowWidth / 1.5}
          //   y={windowHeight / 1.3}
          //   // onShortPressRelease={() => { goToCart(), cartCount() }}
          //   onShortPressRelease={onCartClicked}
          // >
          <TouchableOpacity style={{ position: 'absolute', left: windowWidth / 1.38, top: windowHeight / 1.2, }} onPress={() => { onCartClicked() }}>
            <View
              testID="cartIcon"
              style={{
                width: 80,
                height: 80,
                backgroundColor: Colors.secondaryColor,
                borderRadius: 38,
                justifyContent: "center",
                // backgroundColor: 'red',
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 1,
                },
                shadowOpacity: 0.22,
                shadowRadius: 2.22,
                elevation: 5,
              }}>
              <View style={{ alignSelf: "center" }}>
                <Ionicons name="cart-outline" size={48} color={Colors.mainTxtColor} />
              </View>
              <View style={styles.cartCount}>
                <Text style={{
                  color: Colors.whiteColor,
                  fontSize: 14,
                  fontFamily: "NunitoSans-Bold"
                }}>{cartItems.length > 0 ? cartItems.length : cartItemsProcessed.length}</Text>
              </View>
            </View>
          </TouchableOpacity>
          // </Draggable>
          : <></>
      }

      {
        (showUserHelpPopup) ?
          <Draggable
            shouldReverse={false}
            renderSize={100}
            // renderColor={Colors.secondaryColor}
            isCircle
            x={windowWidth / 1.5}
            y={windowHeight / 1.5}
            // onShortPressRelease={() => { goToCart(), cartCount() }}
            onShortPressRelease={onUserHelpClicked}
          >
            <View style={{
              width: 60,
              height: 60,
              backgroundColor: Colors.lightPrimary,
              borderRadius: 30,
              justifyContent: "center",
              // backgroundColor: 'red',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 1,
              },
              shadowOpacity: 0.22,
              shadowRadius: 2.22,
              elevation: 5,
            }}>
              <View style={{ alignSelf: "center" }}>
                <Ionicons name="help-circle-outline" size={42} color={Colors.mainTxtColor} />
              </View>
              {/* <View style={styles.cartCount}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: "NunitoSans-Bold"
              }}>{cartItems.length}</Text>
            </View> */}
            </View>
          </Draggable>
          : <></>
      }
    </View >
  );
};

const styles = StyleSheet.create({
  cartCount: {
    position: 'absolute',
    top: -6,
    right: -8,
    backgroundColor: Colors.primaryColor,
    width: 32,
    height: 32,
    borderRadius: 32 / 2,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  confirmBox: {
    width: 350,
    // height: windowHeight * 0.35,
    height: 320,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  payment: {
    // color: Colors.descriptionColor,
    color: Colors.mainTxtColor,
    paddingVertical: 5,
    fontSize: 14,
    // marginTop: 20,
    fontFamily: "NunitoSans-SemiBold",
    marginBottom: -5,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 10,
    marginBottom: 10,

    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,

    fontFamily: "NunitoSans-Regular",
    // color: Colors.fieldtTxtColor,
  },
});

export default App;
