import React, { Component, useReducer, useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    TouchableOpacity,
    TextInput,
    FlatList,
    RefreshControl,
    Alert,
    Modal,
    Dimensions,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import { CheckBox } from 'react-native-web';
import Colors from '../constant/Colors';
import * as Cart from '../util/Cart';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import Feather from 'react-native-vector-icons/Feather';
import Icons from 'react-native-vector-icons/EvilIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
// import firebase from "firebase";
import { getFirestore, collection, query, where, getDocs, limit } from "firebase/firestore";
import { ORDER_TYPE_PARSED, USER_ORDER_STATUS, COURIER_CODE, LALAMOVE_STATUS, ORDER_TYPE, MRSPEEDY_STATUS, COURIER_INFO_DICT, SENDER_DROPDOWN_LIST, USER_ORDER_PRIORITY, APP_TYPE, OFFLINE_BILL_TYPE, UNIT_TYPE_SHORT, PRODUCT_PRICE_TYPE, ORDER_TYPE_DETAILS } from '../constant/common';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { Picker } from "react-native";
import { UserStore } from '../store/userStore';
import { CommonActions, useFocusEffect, useLinkTo } from "@react-navigation/native";
//import molpay from "molpay-mobile-xdk-reactnative-beta";
import moment from 'moment';
//import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import { prefix } from "../constant/env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { TempStore } from '../store/tempStore';
import { TableStore } from '../store/tableStore';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
import BigNumber from 'bignumber.js';

import koodoo_logo from "../asset/image/logo.png";
import { checkToApplyScOrNot, checkToApplyTaxOrNot, excludeSkipScItems, isMobile } from '../util/commonFuncs';
import { PaymentStore } from '../store/paymentStore';
import { idbGet } from '../util/db';
import { DataStore } from '../store/dataStore';

const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);



/**
 * OrderDetailScreen
 * function
 * *display detail of the order
 * 
 * route.params
 * *orderId: id of the selected order to be displayed
 */

const ORDER_STATUS_PARSED = {
    PLACED: 'PLACED',
    PREPARING: 'PREPARING',
    PICKING_UP: 'PICKING_UP',
    DELIVERING: 'DELIVERING',
    DELIVERED: 'DELIVERED',
};

const ORDER_STATUS_IMAGES_DELIVERY = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up.png'),
    DELIVERING: require('../asset/image/order-status-delivering.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES = {
    PLACED: require('../asset/image/order-status-placed.png'),
    PREPARING: require('../asset/image/order-status-preparing.png'),
    PICKING_UP: require('../asset/image/order-status-preparing.png'),
    DELIVERING: require('../asset/image/order-status-preparing.png'),
    DELIVERED: require('../asset/image/order-status-delivered.png'),
};

const ORDER_STATUS_IMAGES_BOTTOM = {
    PLACED: require('../asset/image/order-status-placed-bottom.png'),
    PREPARING: require('../asset/image/order-status-preparing-bottom.png'),
    PICKING_UP: require('../asset/image/order-status-picking-up-bottom.png'),
    DELIVERING: require('../asset/image/order-status-delivering-bottom.png'),
    DELIVERED: require('../asset/image/order-status-delivered-bottom.png'),
};

const ORDER_STATUS_TEXT_DELIVERY = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Your driver is picking up your order!',
    DELIVERING: 'Driver on the way delivering your order!',
    DELIVERED: 'Your order has been delivered.\nEnjoy your meal!',
};

const ORDER_STATUS_TEXT = {
    PLACED: 'Your order placed successfully!',
    PREPARING: 'Restaurant is currently preparing your order!',
    PICKING_UP: 'Restaurant is currently preparing your order!',
    DELIVERING: 'Restaurant is currently preparing your order!',
    DELIVERED: 'Your order is ready, enjoy your meal!',
};

/////////////////////////////////////////////////////////////////////////

const ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY = {
    PLACED: require('../asset/image/t-received.png'),
    PREPARING: require('../asset/image/t-preparing.png'),
    DELIVERED: require('../asset/image/t-delivered.png'),
    PICKED_UP: require('../asset/image/order-status-picking-up.png'),
};

const OrderDetailScreen = props => {
    const {
        navigation,
        route,
    } = props;

    const linkTo = useLinkTo();
    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity style={{
            }} onPress={async () => {
                // props.navigation.goBack();
                const subdomain = await AsyncStorage.getItem('latestSubdomain');

                if (!subdomain) {
                    linkTo && linkTo(`${prefix}/outlet/menu`);
                }
                else {
                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                }
            }}>
                <View style={{
                    marginLeft: 10,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                }}>
                    <Ionicons
                        name="chevron-back"
                        size={26}
                        color={Colors.fieldtTxtColor}
                        style={{
                        }}
                    />

                    <Text
                        style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 16,
                            textAlign: 'center',
                            fontFamily: 'NunitoSans-Regular',
                            lineHeight: 22,
                            marginTop: -1,
                        }}>
                        Back
                    </Text>
                </View>
            </TouchableOpacity>
        ),
        headerRight: () => (
            // <TouchableOpacity onPress={() => { props.navigation.navigate('Profile') }} style={{
            // }}>
            //     <View style={{ marginRight: 15 }}>
            //         <Ionicons name="menu" size={30} color={Colors.primaryColor} />
            //     </View>
            // </TouchableOpacity>
            <View></View>
        ),
        headerTitle: () => (
            <View style={{
                justifyContent: 'center',
                alignItems: 'center',
                bottom: -1,
            }}>
                <Text
                    style={{
                        fontSize: 20,
                        lineHeight: 25,
                        textAlign: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.mainTxtColor,
                    }}>
                    Order Details
                </Text>
            </View>
        ),
        headerTintColor: "#000000",
    });

    var orderIdParam = null;
    if (route && route.params) {
        const orderIdParam = route.params.orderId;
    }

    const [orderId, setOrderId] = useState(orderIdParam);
    const [orderData, setOrderData] = useState([]);
    const [orderMerchant, setOrderMerchant] = useState([]);
    const [orderItems, setOrderItems] = useState(null);
    const [orderTax, setOrderTax] = useState([]);

    const [expandDetails, setExpandDetails] = useState(false);
    const [expandCompleteQuestionnaire, setExpandCompleteQuestionnaire] = useState(false);
    const [questionnaireIndex, setQuestionnaireIndex] = useState(1);
    //const current = question[questionnaireIndex];

    const [reviewComment, setReviewComment] = useState('');
    const [reviewTotal, setReviewTotal] = useState(18);

    const [starRatingDefault, setStarRatingDefault] = useState(5);
    const [starRating, setStarRating] = useState([1, 2, 3, 4, 5]);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);

    const setState = () => { };

    const [orderOutlet, setOrderOutlet] = useState({});

    const [orderDetails, setOrderDetails] = useState(null);
    const [orderDetailsDict, setOrderDetailsDict] = useState({});

    const scrollViewRef = useRef();
    const [actionModalVisibility, setActionModalVisibility] = useState(false);
    const [completeModalVisibility, setCompleteModalVisibility] = useState(false);
    const [questionnaireVisibility, setQuestionnaireVisibility] = useState(false);
    const [receiptModal, setReceiptModal] = useState(false);

    const [selectedSender, setSelectedSender] = useState(SENDER_DROPDOWN_LIST[0].value);

    const [deliveryQuotation, setDeliveryQuotation] = useState({
        totalFee: 0,
    });

    /////////////////////////////////////////////////////////

    // 2022-11-22 - To support pick unpaid orders to paid

    const [toRenderOrders, setToRenderOrders] = useState([]);

    const [pendingApprovalOrders, setPendingApprovalOrders] = useState([]);

    /////////////////////////////////////////////////////////

    const selectedUserOrderTakeaway = CommonStore.useState(s => s.selectedUserOrderTakeaway);
    const isLoading = CommonStore.useState(s => s.isLoading);

    const userInfoName = UserStore.useState((s) => s.userInfoName);
    const userInfoPhone = UserStore.useState((s) => s.userInfoPhone);

    /////////////////////////////////////////////////////////

    // const selectedTableOrders = CommonStore.useState(s => s.selectedTableOrders);

    const orderToShow = CommonStore.useState(s => s.orderToShow);

    const selectedUserOrderOthersTakeaway = CommonStore.useState(s => s.selectedUserOrderOthersTakeaway);

    /////////////////////////////////////////////////////////

    const userEmail = UserStore.useState(s => s.email);
    const userName = UserStore.useState(s => s.name);
    const userAvatar = UserStore.useState(s => s.avatar);
    const userAddresses = UserStore.useState(s => s.userAddresses);
    const firebaseUid = UserStore.useState(s => s.firebaseUid);
    const userNumber = UserStore.useState(s => s.number);

    const isPlacingReservation = CommonStore.useState((s) => s.isPlacingReservation);

    ////////////////////////////////////////////////////////

    const selectedUserOrderAnonymousTakeaway = CommonStore.useState((s) => s.selectedUserOrderAnonymousTakeaway);

    ////////////////////////////////////////////////////////

    const selectedSummaryOrdersDict = TableStore.useState(s => s.selectedSummaryOrdersDict);
    const selectedCancelledOrdersDict = TableStore.useState(s => s.selectedCancelledOrdersDict);
    const selectedDeliveredOrdersDict = TableStore.useState(s => s.selectedDeliveredOrdersDict);

    const count = TableStore.useState(s => s.count);

    const cancelUser = TableStore.useState(s => s.cancelUser);
    const deliveredUser = TableStore.useState(s => s.deliveredUser);
    const cancelledUser = TableStore.useState(s => s.cancelledUser);

    const isPay = TableStore.useState(s => s.isPay);

    const orderDisplaySummary = TableStore.useState(s => s.orderDisplaySummary);
    const orderDisplayIndividual = TableStore.useState(s => s.orderDisplayIndividual);
    const orderDisplayProduct = TableStore.useState(s => s.orderDisplayProduct);

    const selectedOrderToPayUserId = CommonStore.useState(s => s.selectedOrderToPayUserId);

    const currCrmUser = CommonStore.useState(s => s.currCrmUser);

    ////////////////////////////////////////////////////////

    useFocusEffect(
        useCallback(() => {
            // setTimeout(() => {
            //     if (global.selectedOutlet === null) {
            //         linkTo && linkTo(`${prefix}/scan`);
            //     }
            // }, 10000);

            // setIsMounted(true);
            // return () => {
            //   setIsMounted(false);
            // };
        }, [])
    );

    useEffect(() => {
        TableStore.update(s => {
            s.orderDisplaySummary = true;
            s.orderDisplayProduct = false;
        });

        CommonStore.update((s) => {
            s.currPage = "";

            s.isLoading = false;
        });
    }, []);

    useEffect(() => {
        if (currCrmUser) {
            CommonStore.update(s => {
                s.selectedOrderToPayUserId = currCrmUser.email;
            });
        }
        else if (userEmail || firebaseUid) {
            CommonStore.update(s => {
                s.selectedOrderToPayUserId = userEmail ? userEmail : firebaseUid;
            });
        }
    }, [currCrmUser, userEmail, firebaseUid]);

    ////////////////////////////////////////////////////////

    useEffect(() => {
        if (selectedOutlet === null) {
            readStates();
        }

        readCommonStates();
    }, [selectedOutlet]);

    const readStates = async () => {
        console.log('global.selectedoutlet = readStates (1) (==test==)');

        if (selectedOutlet === null) {
            console.log('global.selectedoutlet = readStates (2) (==test==)');

            // const commonStoreDataRaw = await AsyncStorage.getItem("@commonStore");
            const commonStoreDataRaw = await idbGet('@commonStore');
            if (commonStoreDataRaw !== undefined) {
                console.log('global.selectedoutlet = readStates (3) (==test==)');

                const commonStoreData = JSON.parse(commonStoreDataRaw);

                const latestOutletId = await AsyncStorage.getItem("latestOutletId");

                console.log('latestOutletId');
                console.log(latestOutletId);
                console.log('commonStoreData.selectedOutlet');
                console.log(commonStoreData.selectedOutlet);

                if (
                    commonStoreData.selectedOutlet &&
                    latestOutletId === commonStoreData.selectedOutlet.uniqueId
                ) {
                    // check if it's the same outlet user scanned

                    console.log('global.selectedoutlet = readStates (4) (==test==)');

                    if (isPlacingReservation) {
                    } else {
                        // if (
                        //   commonStoreData.orderType === ORDER_TYPE.DINEIN 
                        //   // 2022-10-08 - Try to disable this
                        //   // &&
                        //   // commonStoreData.userCart.uniqueId === undefined
                        // ) {
                        //   // logout the user

                        //   linkTo && linkTo(`${prefix}/scan`);
                        // }
                    }

                    console.log('global.selectedoutlet = commonStoreData.selectedOutlet (3) (==test==)');

                    global.selectedOutlet = commonStoreData.selectedOutlet;

                    CommonStore.replace(commonStoreData);

                    // const userStoreDataRaw = await AsyncStorage.getItem("@userStore");
                    const userStoreDataRaw = await idbGet('@userStore');
                    if (userStoreDataRaw !== undefined) {
                        const userStoreData = JSON.parse(userStoreDataRaw);

                        UserStore.replace(userStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    const dataStoreDataRaw = await idbGet('@dataStore');
                    if (dataStoreDataRaw !== undefined) {
                        const dataStoreData = JSON.parse(dataStoreDataRaw);

                        DataStore.replace(dataStoreData);
                        // DataStore.replace({
                        //   ...dataStoreData,
                        //   ...dataStoreData.linkToFunc !== undefined && {
                        //     linkToFunc: dataStoreData.linkToFunc,
                        //   },
                        // });
                    }

                    // const tableStoreDataRaw = await AsyncStorage.getItem("@tableStore");
                    const tableStoreDataRaw = await idbGet('@tableStore');
                    if (tableStoreDataRaw !== undefined) {
                        const tableStoreData = JSON.parse(tableStoreDataRaw);

                        TableStore.replace(tableStoreData);
                    }

                    // const paymentStoreDataRaw = await AsyncStorage.getItem("@paymentStore");
                    const paymentStoreDataRaw = await idbGet('@paymentStore');
                    if (paymentStoreDataRaw !== undefined) {
                        const paymentStoreData = JSON.parse(paymentStoreDataRaw);

                        PaymentStore.replace(paymentStoreData);
                    }

                    // const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
                    // if (dataStoreDataRaw !== undefined) {
                    //   const dataStoreData = JSON.parse(dataStoreDataRaw);

                    //   DataStore.replace(dataStoreData);
                    // }
                }
            }
        }
    };

    const readCommonStates = async () => {
        // if (!linkToFunc) {
        //   const dataStoreDataRaw = await AsyncStorage.getItem('@dataStore');
        //   if (dataStoreDataRaw !== undefined) {
        //     const dataStoreData = JSON.parse(dataStoreDataRaw);
        //     DataStore.replace(dataStoreData);
        //   }
        // }
    };

    ////////////////////////////////////////////////////////

    // useEffect(() => {
    //     if (selectedTableOrders && selectedTableOrders.length > 0) {
    //         CommonStore.update(s => {
    //             s.selectedUserOrderTakeaway = selectedTableOrders[0];

    //             s.selectedUserOrderOthersTakeaway = selectedTableOrders.slice(1);
    //         });
    //     }
    // }, [selectedTableOrders]);

    useEffect(() => {
        // var takeawayOrders = global.takeawayOrders;

        // let takeawayOrders = selectedUserOrderAnonymousTakeaway;
        let takeawayOrders = [];
        let pendingApprovalOrdersTemp = [];

        for (let i = 0; i < selectedUserOrderAnonymousTakeaway.length; i++) {
            if (selectedOutlet.takeawayRequiredAuthorizationBeforePayment) {
                if (selectedUserOrderAnonymousTakeaway[i].orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED) {
                    // haven't authorized yet

                    pendingApprovalOrdersTemp.push(selectedUserOrderAnonymousTakeaway[i]);
                }
                else {
                    takeawayOrders.push(selectedUserOrderAnonymousTakeaway[i]);
                }
            }
            else {
                takeawayOrders.push(selectedUserOrderAnonymousTakeaway[i]);
            }
        }

        let orderToShowTemp = {};

        if (takeawayOrders && takeawayOrders.length > 0) {
            CommonStore.update(s => {
                s.selectedUserOrderTakeaway = takeawayOrders[0];

                s.selectedUserOrderOthersTakeaway = takeawayOrders.slice(1);
            });

            orderToShowTemp = takeawayOrders[0];

            setToRenderOrders(takeawayOrders);
        }
        else {
            CommonStore.update(s => {
                s.selectedUserOrderTakeaway = {};

                s.selectedUserOrderOthersTakeaway = [];
            });

            setToRenderOrders([]);
        }

        setPendingApprovalOrders(pendingApprovalOrdersTemp);

        if (pendingApprovalOrdersTemp.length > 0) {
            if (takeawayOrders.length === 0) {
                CommonStore.update(s => {
                    // s.orderToShow = pendingApprovalOrdersTemp[0];
                });

                orderToShowTemp = pendingApprovalOrdersTemp[0];
            }
        }
        else {
        }

        CommonStore.update(s => {
            s.orderToShow = orderToShowTemp;
        });
    }, [selectedUserOrderAnonymousTakeaway]);

    useEffect(() => {
        if (selectedOutlet && selectedOutlet.uniqueId) {
            setOrderOutlet(selectedOutlet);
        }
        else {

        }
    }, [selectedOutlet]);

    // console.log('selectedTableOrders');
    // console.log(selectedTableOrders);
    // console.log('selectedUserOrderTakeaway');
    // console.log(selectedUserOrderTakeaway);
    // console.log('selectedUserOrderOthersTakeaway');
    // console.log(selectedUserOrderOthersTakeaway);

    ////////////////////////////////////////////////////////   

    // useEffect(() => {
    //     if (!selectedOutlet && selectedUserOrderTakeaway && selectedUserOrderTakeaway.outletId) {
    //         retrieveOutletData();
    //     }
    // }, [selectedOutlet, selectedUserOrderTakeaway]);

    // const retrieveOutletData = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrderTakeaway.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         CommonStore.update(s => {
    //             s.selectedOutlet = outletSnapshot.docs[0].data();
    //         });
    //     }
    // };

    // useEffect(() => {
    //     if (selectedUserOrderTakeaway && selectedUserOrderTakeaway.uniqueId) {
    //         if (orderOutlet.uniqueId === undefined) {
    //             retrieveOrderOutlet();
    //         }
    //     }
    // }, [selectedUserOrderTakeaway, orderOutlet]);

    // const retrieveOrderOutlet = async () => {
    //     const outletSnapshot = await firebase.firestore().collection(Collections.Outlet)
    //         .where('uniqueId', '==', selectedUserOrderTakeaway.outletId)
    //         .limit(1)
    //         .get();

    //     if (!outletSnapshot.empty) {
    //         const outlet = outletSnapshot.docs[0].data();

    //         setOrderOutlet(outlet);
    //     }
    // };

    const [parsedOrderStatus, setParsedOrderStatus] = useState(ORDER_STATUS_PARSED.PLACED);
    const [parsedOrderStatusDict, setParsedOrderStatusDict] = useState({});

    // const selectedUserOrderTakeaway = CommonStore.useState(s => s.selectedUserOrderTakeaway)

    useEffect(() => {
        if (selectedUserOrderTakeaway && selectedUserOrderTakeaway.uniqueId) {
            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                // means is still cooking

                setParsedOrderStatus(ORDER_STATUS_PARSED.PREPARING);
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                // means done cooking

                if (selectedUserOrderTakeaway.courierCode === COURIER_CODE.LALAMOVE) {
                    // means is delivery and lalamove

                    if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.REJECTED ||
                        selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.CANCELED ||
                        selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.EXPIRED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.ASSIGNING_DRIVER ||
                        selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.ON_GOING) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.PICKED_UP) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.COMPLETED) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }
                else if (selectedUserOrderTakeaway.courierCode === COURIER_CODE.MRSPEEDY) {
                    // means is delivery and mrspeedy

                    if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.reactivated ||
                        selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.canceled ||
                        selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.delayed ||
                        selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.draft) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }

                    if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.new ||
                        selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.available) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);
                    }
                    else if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.active) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);
                    }
                    else if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.completed) {
                        setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                    }
                }

                if (selectedUserOrderTakeaway.orderType === ORDER_TYPE.PICKUP) {
                    setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
                }
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by merchant.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                });
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order cancelled by user.',

                    orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                });
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                });
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: 'Order canceled.',

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                });
            }

            if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED) {
                setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                setOrderDetails({
                    message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                });
            }

            ////////////////////////////////////////

            setDeliveryQuotation({
                totalFee: selectedUserOrderTakeaway.deliveryFee,
            });
        }
    }, [selectedUserOrderTakeaway]);

    useEffect(() => {
        if (selectedUserOrderOthersTakeaway && selectedUserOrderOthersTakeaway.length > 0) {
            for (var i = 0; i < selectedUserOrderOthersTakeaway.length; i++) {
                var selectedUserOrderTakeaway = selectedUserOrderOthersTakeaway[i];

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                    selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
                    // means is still cooking

                    setParsedOrderStatusDict({
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.PREPARING,
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
                    // means done cooking

                    if (selectedUserOrderTakeaway.courierCode === COURIER_CODE.LALAMOVE) {
                        // means is delivery and lalamove

                        if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.REJECTED ||
                            selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.CANCELED ||
                            selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.EXPIRED) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }

                        if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.ASSIGNING_DRIVER ||
                            selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.ON_GOING) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }
                        else if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.PICKED_UP) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERING,
                            });
                        }
                        else if (selectedUserOrderTakeaway.courierStatus === LALAMOVE_STATUS.COMPLETED) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                            });
                        }
                    }
                    else if (selectedUserOrderTakeaway.courierCode === COURIER_CODE.MRSPEEDY) {
                        // means is delivery and mrspeedy

                        if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.reactivated ||
                            selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.canceled ||
                            selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.delayed ||
                            selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.draft) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }

                        if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.new ||
                            selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.available) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.PICKING_UP);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.PICKING_UP,
                            });
                        }
                        else if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.active) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERING);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERING,
                            });
                        }
                        else if (selectedUserOrderTakeaway.courierStatus === MRSPEEDY_STATUS.completed) {
                            // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                            setParsedOrderStatusDict({
                                ...parsedOrderStatusDict,
                                [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                            });
                        }
                    }

                    if (selectedUserOrderTakeaway.orderType === ORDER_TYPE.PICKUP) {
                        // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                        setParsedOrderStatusDict({
                            ...parsedOrderStatusDict,
                            [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                        });
                    }
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
                    selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled by merchant.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrderTakeaway.uniqueId]: {
                            message: 'Order cancelled by merchant.',

                            orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
                        },
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled by user.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrderTakeaway.uniqueId]: {
                            message: 'Order cancelled by user.',

                            orderStatus: USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,
                        },
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_REJECTED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrderTakeaway.uniqueId]: {
                            message: `Order rejected by ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
                        },
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_CANCELED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: 'Order cancelled.',

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrderTakeaway.uniqueId]: {
                            message: 'Order cancelled.',

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
                        },
                    });
                }

                if (selectedUserOrderTakeaway.orderStatus === USER_ORDER_STATUS.ORDER_SENDER_EXPIRED) {
                    // setParsedOrderStatus(ORDER_STATUS_PARSED.DELIVERED);

                    setParsedOrderStatusDict({
                        ...parsedOrderStatusDict,
                        [selectedUserOrderTakeaway.uniqueId]: ORDER_STATUS_PARSED.DELIVERED,
                    });

                    // setOrderDetails({
                    //     message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                    //     orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                    // });

                    setOrderDetailsDict({
                        ...orderDetailsDict,
                        [selectedUserOrderTakeaway.uniqueId]: {
                            message: `Order expired in ${COURIER_INFO_DICT[selectedUserOrderTakeaway.courierCode].name} side.`,

                            orderStatus: USER_ORDER_STATUS.ORDER_SENDER_EXPIRED,
                        },
                    });
                }

                ////////////////////////////////////////

                // setDeliveryQuotation({
                //     totalFee: selectedUserOrderTakeaway.deliveryFee,
                // });
            }
        }
    }, [selectedUserOrderOthersTakeaway]);

    useEffect(() => {
        if (actionModalVisibility) {
            if (selectedUserOrderTakeaway.crUserAddress) {
                // valid order to proceed

                if (selectedSender === COURIER_CODE.LALAMOVE) {
                    var body = {
                        outletLat: selectedUserOrderTakeaway.crOutletLat,
                        outletLng: selectedUserOrderTakeaway.crOutletLng,
                        outletAddress: selectedUserOrderTakeaway.crOutletAddress,

                        outletPhone: selectedUserOrderTakeaway.crOutletPhone,
                        outletName: selectedUserOrderTakeaway.outletName,

                        userLat: selectedUserOrderTakeaway.crUserLat,
                        userLng: selectedUserOrderTakeaway.crUserLng,
                        userAddress: selectedUserOrderTakeaway.crUserAddress,

                        userName: selectedUserOrderTakeaway.crUserName,
                        userPhone: selectedUserOrderTakeaway.crUserPhone,
                        userRemarks: selectedUserOrderTakeaway.crUserRemarks,

                        // scheduleAt: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
                        console.log("lalamove quotation result");
                        console.log(result);

                        if (result === undefined) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result && result.totalFee) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.totalFee),
                                totalFeeCurrency: result.totalFeeCurrency,
                                courierCode: COURIER_CODE.LALAMOVE,
                            });
                        }
                    });
                }
                else if (selectedSender === COURIER_CODE.MRSPEEDY) {
                    var body = {
                        outletLat: selectedUserOrderTakeaway.crOutletLat,
                        outletLng: selectedUserOrderTakeaway.crOutletLng,
                        outletAddress: selectedUserOrderTakeaway.crOutletAddress,

                        outletPhone: selectedUserOrderTakeaway.crOutletPhone,
                        outletName: selectedUserOrderTakeaway.outletName,

                        userLat: selectedUserOrderTakeaway.crUserLat,
                        userLng: selectedUserOrderTakeaway.crUserLng,
                        userAddress: selectedUserOrderTakeaway.crUserAddress,

                        userName: selectedUserOrderTakeaway.crUserName,
                        userPhone: selectedUserOrderTakeaway.crUserPhone,
                        userRemarks: selectedUserOrderTakeaway.crUserRemarks,

                        totalWeightKg: selectedUserOrderTakeaway.totalWeightKg,
                        // outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
                        // outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
                        // userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
                        // userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
                    };

                    console.log('quotation body');
                    console.log(body);

                    ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
                        console.log("mr speedy quotation result");
                        console.log(result);

                        if (!result || !result.is_successful) {
                            // means lalamove can't deliver to this address

                            Alert.alert(
                                'Info',
                                'Sorry, we unable to deliver to this address, please try another one.',
                            );
                        }
                        else if (result.is_successful && result.order && result.order.payment_amount) {
                            // { totalFee: "0.00", totalFeeCurrency: "MYR" }

                            setDeliveryQuotation({
                                totalFee: parseFloat(result.order.payment_amount),
                                totalFeeCurrency: 'MYR',
                                courierCode: COURIER_CODE.MRSPEEDY,
                            });
                        }
                    });
                }
            }
            else {
                // do nothing for now
            }
        }
    }, [actionModalVisibility, selectedUserOrderTakeaway, selectedSender]);

    // componentDidMount() {
    //     ApiClient.GET(API.order2 + orderId).then((result) => {
    //         console.log(result);
    //         setState({
    //             orderId: result,
    //             orderItems: result.orderItems,
    //             orderData: result.outlet,
    //             orderMerchant: result.outlet.merchant,
    //             orderTax: result.outlet.orderTax,
    //         });
    //     });
    // }

    const renderTableOrder = ({ item, index }) => {
        var styleStripEffect = {};
        var checkboxShowed = true;

        var isPaidItem = false;

        if (item.priceToPay !== undefined && item.priceToPay === item.price) {
            isPaidItem = true;

            styleStripEffect = {
                // textDecorationLine: 'line-through',
                color: Colors.primaryColor,
            };
            checkboxShowed = false;
        }

        return (
            <>
                {
                    item.priceToPay === undefined ||
                        (item.priceToPay !== undefined && item.priceToPay === item.price)
                        ?
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                        }}>
                            {/* <View style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                // width: '2%',
                                width: '5%',
                                // backgroundColor: 'red',
                            }}>
                                <View
                                    style={{
                                        // width: '7.5%',
                                        // height: '2%',
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.34,
                                        shadowRadius: 3.32,
                                        elevation: 1,
                                        //backgroundColor: 'blue',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        // borderWidth: 0,
                                    }}>
                                    {!item.priceToPay || item.priceToPay === 0 ? (
                                        <CheckBox
                                            disabled={!checkboxShowed}
                                            value={
                                                selectedSummaryOrdersDict[
                                                item.itemId + item.cartItemDate.toString()
                                                ] !== false &&
                                                selectedSummaryOrdersDict[
                                                item.itemId + item.cartItemDate.toString()
                                                ] !== undefined
                                            }
                                            onValueChange={(value) => {
                                                // console.log(count, 'count');                      

                                                if (value) {
                                                    // setCount(count + 1);

                                                    TableStore.update(s => {
                                                        s.count = count + 1;
                                                    });
                                                } else {
                                                    // setCount(count - 1);

                                                    TableStore.update(s => {
                                                        s.count = count - 1;
                                                    });
                                                }

                                                TableStore.update(s => {
                                                    s.isDelivered = false;
                                                    s.isCancelled = false;
                                                    s.cancelledCount = 0;
                                                    s.deliveryCount = 0;
                                                    s.deliveredUser = [];
                                                    s.cancelledUser = [];
                                                    s.selectedCancelledOrdersDict = {};
                                                    s.selectedDeliveredOrdersDict = {};
                                                    s.selectedSummaryOrdersDict = {
                                                        ...selectedSummaryOrdersDict,
                                                        [item.itemId + item.cartItemDate.toString()]: value,
                                                    };
                                                });

                                                if (value) {
                                                    var tempAdd = [...cancelUser];
                                                    tempAdd.push({
                                                        cartItemDate: item.cartItemDate,
                                                        itemId: item.itemId,
                                                        userOrderId: item.userOrderId ? item.userOrderId : '',
                                                        quantity: item.quantity,
                                                        discountPromotions: item.discountPromotions ? item.discountPromotions : 0,
                                                        isFreeItem: item.isFreeItem ? item.isFreeItem : false,
                                                    });

                                                    // setCancelUser(tempAdd);

                                                    TableStore.update(s => {
                                                        s.cancelUser = tempAdd;
                                                    });
                                                } else {
                                                    var tempMinus = [...cancelUser];
                                                    for (let i = 0; i < tempMinus.length; i++) {
                                                        if (
                                                            tempMinus[i].cartItemDate === item.cartItemDate &&
                                                            tempMinus[i].itemId === item.itemId
                                                        ) {
                                                            tempMinus.splice(i, 1);
                                                            break;
                                                        }
                                                    }

                                                    // setCancelUser(tempMinus);

                                                    TableStore.update(s => {
                                                        s.cancelUser = tempMinus;
                                                    });
                                                }
                                            }}
                                        />
                                    ) : null}
                                </View>
                            </View> */}

                            <View style={{
                                flexDirection: 'column',
                                // alignItems: 'center',
                                width: '100%',
                                // backgroundColor: 'blue',
                            }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 5, width: '100%' }}>
                                    {/* 1/3/23 Hide first */}
                                    {/* <View style={{
                                        width: isMobile() ? '5%' : '3%',
                                        marginRight: 10,

                                        // opacity: item.isCancelledItem ? 0 : 100,
                                    }}>
                                        <Checkbox
                                            name={`my-checkbox-${index}`}
                                            checked={isAllPaid ||
                                                (selectedProductOrdersDict[
                                                    item.userOrderId +
                                                    item.itemId +
                                                    item.cartItemDate.toString()
                                                ] !== false &&
                                                    selectedProductOrdersDict[
                                                    item.userOrderId +
                                                    item.itemId +
                                                    item.cartItemDate.toString()
                                                    ] !== undefined)}
                                            onChange={(e) => {
                                                setSelectedProductOrdersDict({
                                                    ...selectedProductOrdersDict,
                                                    [item.userOrderId +
                                                        item.itemId +
                                                        item.cartItemDate.toString()]: !selectedProductOrdersDict[item.userOrderId +
                                                        item.itemId +
                                                        item.cartItemDate.toString()],
                                                });
                                            }}
                                            style={{
                                                // backgroundColor: Colors.primaryColor,
                                                // color: Colors.primaryColor,
                                                // borderWidth: 1,
                                                // borderColor: Colors.primaryColor,
                                            }}
                                            disabled={isAllPaid || item.isCancelledItem}
                                        />
                                    </View> */}
                                    <View style={{ width: "10%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>#{item.orderId}{` (${item.userName})`}</Text>
                                    </View>
                                    <View style={{ width: isMobile() ? '52%' : "54%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>{item.name}{item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''}{isPaidItem ? ' (Paid)' : ''}</Text>
                                    </View>
                                    <View style={{ width: "10%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}>X{item.quantity}</Text>
                                    </View>
                                    <View style={{ width: "5%" }}>
                                        <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10 }, styleStripEffect]}></Text>
                                    </View>
                                    <View style={{ width: "15%" }}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', }}>
                                            <Text style={[{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", textDecorationLine: item.isCancelledItem ? 'line-through' : 'none', }, styleStripEffect]}>RM </Text>
                                            <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, textDecorationLine: item.isCancelledItem ? 'line-through' : 'none', }, styleStripEffect]}>{(item.price + (item.discountPromotions ? item.discountPromotions : 0)).toFixed(2)}</Text>

                                            {item.isCancelledItem ? <Text style={[{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }, styleStripEffect]}>{`\n(Rejected)`}</Text> : <></>}
                                        </View>
                                    </View>
                                </View>

                                {item.remarks && item.remarks.length > 0 ?
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, width: '100%' }}>
                                        <View style={{ width: "60%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}><Text style={{ color: '#a8a8a8' }}>- </Text>{item.remarks}</Text>
                                        </View>
                                        <View style={{ width: "10%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                        <View style={{ width: "5%" }}>
                                            <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                        <View style={{ width: "25%" }}>
                                            <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }, styleStripEffect]}></Text>
                                        </View>
                                    </View>
                                    : <></>
                                }

                                {item.addOns.map((addOnChoice, i) => {
                                    const addOnChoices = addOnChoice.choiceNames.join(", ");
                                    return (
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                            <View style={{ width: "60%", }}>
                                                <View style={{ flexDirection: 'row', }}>
                                                    <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }, styleStripEffect]}>{`${addOnChoice.name}:`}</Text>
                                                    <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }, styleStripEffect]}>{`${addOnChoices}`}</Text>
                                                </View>
                                            </View>
                                            <View style={{ width: "10%" }}>
                                                <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>{`${addOnChoice.quantities
                                                    ? `x${addOnChoice.quantities[0]}`
                                                    : ''
                                                    }`}</Text>
                                            </View>
                                            <View style={{ width: "5%" }}>
                                                <Text style={[{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}></Text>
                                            </View>
                                            <View style={{ width: "25%" }}>
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>RM </Text>
                                                    <Text style={[{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }, styleStripEffect]}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                </View>
                                            </View>
                                        </View>
                                    )
                                })}
                            </View>

                        </View>
                        :
                        <></>
                }
            </>
        );
    };

    const proceedToPaymentPage = async () => {
        if (toRenderOrders && toRenderOrders.length > 0) {
            var selectedOrderToPayListTemp = [];
            var selectedOrdersCartItemsTemp = [];

            var orderToProceedList = [];

            let tempTotalPriceTaxDict = {};

            orderToProceedList = toRenderOrders;

            if (orderDisplaySummary) {
                // for joining all orders into one (summary)
                for (var i = 0; i < orderToProceedList.length; i++) {
                    if (orderToProceedList[i].paymentDetails === null) {
                        var isOrderFullyPaid = true;

                        for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
                            const cartItem = orderToProceedList[i].cartItems[j];

                            if (cartItem.priceToPay === undefined) {
                                selectedOrdersCartItemsTemp.push({
                                    isFreeItem: cartItem.isFreeItem || false,
                                    promotionId: cartItem.promotionId || '',
                                    voucherId: cartItem.voucherId || '',

                                    promotionIdList: cartItem.promotionIdList ? cartItem.promotionIdList : [],

                                    priceOriginal: cartItem.priceOriginal || cartItem.price,

                                    addOns: cartItem.addOns,
                                    cartItemDate: moment(cartItem.cartItemDate).valueOf(),
                                    choices: cartItem.choices,
                                    cookedAt: cartItem.cookedAt,
                                    deliveredAt: cartItem.deliveredAt,
                                    fireOrder: cartItem.fireOrder,
                                    image: cartItem.image,
                                    isChecked: cartItem.isChecked,
                                    itemId: cartItem.itemId,
                                    itemName: cartItem.itemName,
                                    name: cartItem.name,
                                    orderType: cartItem.orderType,
                                    prepareTime: cartItem.prepareTime,
                                    price: cartItem.price,
                                    quantity: cartItem.quantity,
                                    remarks: cartItem.remarks,

                                    userOrderId: orderToProceedList[i].uniqueId,

                                    printerAreaList: cartItem.printerAreaList || [],
                                    printingTypeList: cartItem.printingTypeList || [],

                                    itemSku: cartItem.itemSku || null,
                                    categoryId: cartItem.categoryId || null,

                                    discountPromotions: cartItem.discountPromotions || 0,
                                    discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
                                    discount: cartItem.discount || 0,

                                    isDocket: cartItem.isDocket || false,
                                    printDocketQuantity: cartItem.printDocketQuantity || 1,

                                    originCartItemId: cartItem.originCartItemId || null,

                                    extraPrice: cartItem.extraPrice || 0,

                                    ...(cartItem.priceBundle !== undefined) && {
                                        priceBundle: cartItem.priceBundle,
                                    },

                                    ...(cartItem.priceVariable !== undefined) && {
                                        priceVariable: cartItem.priceVariable,
                                    },
                                });

                                isOrderFullyPaid = false;
                            }
                        }

                        if (!isOrderFullyPaid) {
                            selectedOrderToPayListTemp.push(orderToProceedList[i]);
                        }
                    }
                }
            }
            // else if (orderDisplayIndividual) {
            //     const selectedIndividualOrderIdList = Object.entries(
            //         selectedIndividualOrdersDict,
            //     ).map(([key, value]) => ({ key: key, value: value }));

            //     var validIndividualOrderIdList = [];
            //     for (var i = 0; i < selectedIndividualOrderIdList.length; i++) {
            //         if (selectedIndividualOrderIdList[i].value) {
            //             validIndividualOrderIdList.push(
            //                 selectedIndividualOrderIdList[i].key,
            //             );
            //         }
            //     }

            //     if (validIndividualOrderIdList.length <= 0) {
            //         Alert.alert('Info', 'Please select an least one order to proceed');
            //         return;
            //     }

            //     for (var i = 0; i < orderToProceedList.length; i++) {
            //         if (
            //             validIndividualOrderIdList.includes(
            //                 orderToProceedList[i].uniqueId,
            //             ) &&
            //             orderToProceedList[i].paymentDetails === null
            //         ) {
            //             var isOrderFullyPaid = true;

            //             for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
            //                 const cartItem = orderToProceedList[i].cartItems[j];

            //                 if (cartItem.priceToPay === undefined) {
            //                     selectedOrdersCartItemsTemp.push({
            //                         isFreeItem: cartItem.isFreeItem || false,
            //                         promotionId: cartItem.promotionId || '',

            //                         priceOriginal: cartItem.priceOriginal || cartItem.price,

            //                         addOns: cartItem.addOns,
            //                         cartItemDate: moment(cartItem.cartItemDate).valueOf(),
            //                         choices: cartItem.choices,
            //                         cookedAt: cartItem.cookedAt,
            //                         deliveredAt: cartItem.deliveredAt,
            //                         fireOrder: cartItem.fireOrder,
            //                         image: cartItem.image,
            //                         isChecked: cartItem.isChecked,
            //                         itemId: cartItem.itemId,
            //                         itemName: cartItem.itemName,
            //                         name: cartItem.name,
            //                         orderType: cartItem.orderType,
            //                         prepareTime: cartItem.prepareTime,
            //                         price: cartItem.price,
            //                         quantity: cartItem.quantity,
            //                         remarks: cartItem.remarks,

            //                         userOrderId: orderToProceedList[i].uniqueId,

            //                         printerAreaList: cartItem.printerAreaList || [],

            //                         itemSku: cartItem.itemSku || null,
            //                         categoryId: cartItem.categoryId || null,

            //                         discountPromotions: cartItem.discountPromotions || 0,
            //                         discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
            //                         discount: cartItem.discount || 0,

            //                         isDocket: cartItem.isDocket || false,

            //                         originCartItemId: cartItem.originCartItemId || null,

            //                         extraPrice: cartItem.extraPrice || 0,

            //                         ...(cartItem.priceBundle !== undefined) && {
            //                             priceBundle: cartItem.priceBundle,
            //                         },

            //                         ...(cartItem.priceVariable !== undefined) && {
            //                             priceVariable: cartItem.priceVariable,
            //                         },
            //                     });

            //                     isOrderFullyPaid = false;
            //                 }
            //             }

            //             if (!isOrderFullyPaid) {
            //                 selectedOrderToPayListTemp.push(orderToProceedList[i]);
            //             }
            //         }
            //     }
            // } else if (orderDisplayProduct) {
            //     const selectedProductOrderIdList = Object.entries(
            //         selectedProductOrdersDict,
            //     ).map(([key, value]) => ({ key: key, value: value }));

            //     var validProductOrderIdList = [];

            //     for (var i = 0; i < selectedProductOrderIdList.length; i++) {
            //         if (selectedProductOrderIdList[i].value) {
            //             validProductOrderIdList.push(selectedProductOrderIdList[i].key);
            //         }
            //     }

            //     validProductOrderIdList = [...new Set(validProductOrderIdList)];

            //     var validCartItemList = [];
            //     var validOrderIdList = [];
            //     for (var i = 0; i < validProductOrderIdList.length; i++) {
            //         validOrderIdList.push(validProductOrderIdList[i].slice(0, 36));
            //         // validCartItemList.push({
            //         //   itemId: validProductOrderIdList[i].slice(36, 72),
            //         //   cartItemDate: validProductOrderIdList[i].slice(72, 85),
            //         // });
            //         validCartItemList.push(validProductOrderIdList[i].slice(36, 85));
            //     }

            //     if (validProductOrderIdList.length <= 0) {
            //         Alert.alert('Info', 'Please select at least one product to proceed');
            //         return;
            //     }

            //     for (var i = 0; i < orderToProceedList.length; i++) {
            //         if (
            //             validOrderIdList.includes(orderToProceedList[i].uniqueId) &&
            //             orderToProceedList[i].paymentDetails === null
            //         ) {
            //             var isOrderFullyPaid = true;

            //             for (var j = 0; j < orderToProceedList[i].cartItems.length; j++) {
            //                 const cartItem = orderToProceedList[i].cartItems[j];

            //                 if (
            //                     cartItem.priceToPay === undefined &&
            //                     validCartItemList.includes(
            //                         cartItem.itemId + cartItem.cartItemDate.toString(),
            //                     )
            //                 ) {
            //                     selectedOrdersCartItemsTemp.push({
            //                         isFreeItem: cartItem.isFreeItem || false,
            //                         promotionId: cartItem.promotionId || '',

            //                         priceOriginal: cartItem.priceOriginal || cartItem.price,

            //                         addOns: cartItem.addOns,
            //                         cartItemDate: moment(cartItem.cartItemDate).valueOf(),
            //                         choices: cartItem.choices,
            //                         cookedAt: cartItem.cookedAt,
            //                         deliveredAt: cartItem.deliveredAt,
            //                         fireOrder: cartItem.fireOrder,
            //                         image: cartItem.image,
            //                         isChecked: cartItem.isChecked,
            //                         itemId: cartItem.itemId,
            //                         itemName: cartItem.itemName,
            //                         name: cartItem.name,
            //                         orderType: cartItem.orderType,
            //                         prepareTime: cartItem.prepareTime,
            //                         price: cartItem.price,
            //                         quantity: cartItem.quantity,
            //                         remarks: cartItem.remarks,

            //                         userOrderId: orderToProceedList[i].uniqueId,

            //                         printerAreaList: cartItem.printerAreaList || [],

            //                         itemSku: cartItem.itemSku || null,
            //                         categoryId: cartItem.categoryId || null,

            //                         discountPromotions: cartItem.discountPromotions || 0,
            //                         discountPromotionsLCC: cartItem.discountPromotionsLCC || 0,
            //                         discount: cartItem.discount || 0,

            //                         isDocket: cartItem.isDocket || false,

            //                         originCartItemId: cartItem.originCartItemId || null,

            //                         extraPrice: cartItem.extraPrice || 0,

            //                         ...(cartItem.priceBundle !== undefined) && {
            //                             priceBundle: cartItem.priceBundle,
            //                         },

            //                         ...(cartItem.priceVariable !== undefined) && {
            //                             priceVariable: cartItem.priceVariable,
            //                         },
            //                     });

            //                     isOrderFullyPaid = false;
            //                 }
            //             }

            //             if (!isOrderFullyPaid) {
            //                 selectedOrderToPayListTemp.push(orderToProceedList[i]);
            //             }
            //         }
            //     }
            // }

            if (
                selectedOrderToPayListTemp.reduce(
                    (accu, order) => accu + order.cartItems.length,
                    0,
                ) <= 0
            ) {
                window.confirm(
                    'No orders to pay for now',
                );
                return;
            }

            if (selectedOrdersCartItemsTemp.length <= 0) {
                window.confirm(
                    'Orders has been fully paid',
                );
                return;
            }

            const totalDiscAamTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.discAam || 0),
                [],
            );

            let totalPriceTemp = selectedOrdersCartItemsTemp.reduce(
                (accum, cartItem) => {
                    if (cartItem.tId === 'default' || cartItem.tId === undefined) {
                        if (tempTotalPriceTaxDict['default'] !== undefined) {
                            tempTotalPriceTaxDict['default'] += cartItem.price;
                        }
                        else {
                            tempTotalPriceTaxDict['default'] = cartItem.price;
                        }
                    }
                    else {
                        if (tempTotalPriceTaxDict[cartItem.tId] !== undefined) {
                            tempTotalPriceTaxDict[cartItem.tId] += cartItem.price;
                        }
                        else {
                            tempTotalPriceTaxDict[cartItem.tId] = cartItem.price;
                        }
                    }

                    return BigNumber(accum).plus(cartItem.price).toNumber();
                },
                0,
            );

            if (totalDiscAamTemp > 0) {
                totalPriceTemp = BigNumber(totalPriceTemp).minus(totalDiscAamTemp).toNumber();
            }

            let totalPriceTaxList = [{
                key: '',
                totalPrice: totalPriceTemp,
                tax: 0,
                tRate: 0,
                tCode: '',
                tName: 'SST',
            }];

            var taxTemp = 0;
            // if (outletsTaxDict[currOutletId]) {
            //   taxTemp = totalPriceTemp * outletsTaxDict[currOutletId].rate;
            // }
            if (checkToApplyTaxOrNot(selectedOutlet, ORDER_TYPE.PICKUP)) {
                // taxTemp = totalPriceTemp * selectedOutlet.taxRate;

                let discAamLeft = totalDiscAamTemp;

                let totalTaxTemp = 0;
                const tempTotalPriceTaxList = Object.entries(tempTotalPriceTaxDict).map(
                    ([key, value]) => {
                        let taxTemp = 0;
                        let tRate = selectedOutlet.taxRate;
                        let tCode = 'SST';
                        let tName = 'SST';

                        if (value > discAamLeft) {
                            value = value - discAamLeft;
                            discAamLeft = 0;
                        }
                        else {
                            value = 0;
                            discAamLeft = discAamLeft - value;
                        }

                        if (key === 'default') {
                            taxTemp = BigNumber(taxTemp).plus(
                                BigNumber(value).multipliedBy(tRate)
                            ).toNumber();
                        }
                        else {
                            // let foundCustomTax = outletCustomTaxList.find(customTax => customTax.uniqueId === key);
                            let foundCustomTaxCartItem = [...selectedOrdersCartItemsTemp.map(cartItem => (cartItem))]
                                .find(customTaxCartItem => customTaxCartItem.tId === key);

                            if (foundCustomTaxCartItem) {
                                tRate = foundCustomTaxCartItem.tRate;
                                tCode = foundCustomTaxCartItem.tCode;
                                tName = foundCustomTaxCartItem.tName;

                                taxTemp = BigNumber(taxTemp).plus(
                                    BigNumber(value).multipliedBy(foundCustomTaxCartItem.tRate)
                                ).toNumber();
                            }
                            else {
                                taxTemp = BigNumber(taxTemp).plus(
                                    BigNumber(value).multipliedBy(tRate)
                                ).toNumber();
                            }
                        }

                        totalTaxTemp = BigNumber(totalTaxTemp).plus(taxTemp).toNumber();

                        return {
                            key: key,
                            totalPrice: value,
                            tax: taxTemp,
                            tRate: tRate,
                            tCode: tCode,
                            tName: tName,
                        };
                    },
                );

                taxTemp = totalTaxTemp;

                totalPriceTaxList = tempTotalPriceTaxList;
            }

            var scTemp = 0;
            // if (outletsTaxDict[currOutletId]) {
            //   taxTemp = totalPriceTemp * outletsTaxDict[currOutletId].rate;
            // }
            // if (selectedOutlet.scActive && selectedOutlet.scOrderTypes.includes(orderType)) {
            if (checkToApplyScOrNot(selectedOutlet, ORDER_TYPE.PICKUP)) {
                // scTemp = totalPriceTemp * selectedOutlet.scRate;

                scTemp = excludeSkipScItems(totalPriceTemp * selectedOutlet.scRate, selectedOrdersCartItemsTemp, selectedOutlet.scRate);
            }

            const tablePaxTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum + order.tablePax,
                0,
            );
            const totalPrepareTimeTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum + order.totalPrepareTime,
                0,
            );
            const estimatedPreparedDateTemp = moment(
                selectedOrderToPayListTemp[0].orderDate,
            )
                .add(totalPrepareTimeTemp, 'second')
                .valueOf();
            const remarksTemp = selectedOrderToPayListTemp
                .filter(order => order.remarks)
                .map((order) => order.remarks)
                .join('\n');

            const orderIdTemp = selectedOrderToPayListTemp
                .sort((a, b) => a.orderId.localeCompare(b.orderId))
                .map((order) => order.orderId)
                .join(', ');

            const totalPromotionIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.promotionIdList || []),
                [],
            );

            const totalPromoCodePromotionIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.promoCodePromotionIdList || []),
                [],
            );

            const totalDiscAamListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => accum.concat(order.discAamList || []),
                [],
            );

            // might got multiple order that claimed same voucher (multiple customer claim voucher is allowed in configuration), thus no need use Set to ensure unique
            const totalTaggableVoucherIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => {
                    if (order.taggableVoucherId) {
                        return ([...accum, order.taggableVoucherId]);
                    }
                    else {
                        return accum;
                    }
                },
                [],
            );

            const totalUserTaggableVoucherIdListTemp = selectedOrderToPayListTemp.reduce(
                (accum, order) => {
                    if (order.userTaggableVoucherId) {
                        return ([...accum, order.userTaggableVoucherId]);
                    }
                    else {
                        return accum;
                    }
                },
                [],
            );

            // const merchantSnapshot = await firestore().collection(Collections.Merchant)
            //   .where('uniqueId', '==', outlet.merchantId)
            //   .limit(1)
            //   .get();

            // var merchant = null;
            // if (!merchantSnapshot.empty) {
            //   merchant = merchantSnapshot.docs[0].data();
            // }

            const finalPrice =
                BigNumber(
                    Math.round(
                        BigNumber((BigNumber(totalPriceTemp).plus(taxTemp).plus(scTemp).toNumber())).multipliedBy(20).toNumber()
                    )
                ).dividedBy(20).toNumber();
            const finalPriceBefore = BigNumber(totalPriceTemp).plus(taxTemp).plus(scTemp).toNumber();

            // const selectedOrderToPayUser =
            //     selectedOrderToPayUserIdDict[selectedOrderToPayUserId];

            const selectedOrderToPayUser = currCrmUser;

            var toPaidUser = null;
            // if (selectedOrderToPayUserId.includes('@')) {
            //     // means is email

            //     toPaidUser = crmUsers.find(user => user.email === selectedOrderToPayUserId);
            // }
            // else {
            //     // means is firebaseuid

            //     toPaidUser = crmUsers.find(user => user.userId === selectedOrderToPayUserId);
            // }

            // if (!toPaidUser) {
            //     // if still not found

            //     toPaidUser = crmUsers.find(user => user.number === selectedOrderToPayUser.userPhone);
            // }
            toPaidUser = currCrmUser;

            ////////////////////////////////////////////////////

            // Herks - 2022/05/30 - Fixes

            if (selectedOrderToPayUser) {
                if (selectedOrderToPayUser.userPhone === '' &&
                    selectedOrderToPayUser.userId === '' &&
                    selectedOrderToPayUserId === '') {
                    toPaidUser = null;
                }
            }
            else {
                toPaidUser = null;
            }

            // if (toPaidUser === null || selectedOrderToPayUser === null) {
            // }

            ////////////////////////////////////////////////////

            var currPendingOrderTemp = {
                uniqueId: uuidv4(),
                // userId: toPaidUser ? (toPaidUser.firebaseUid || toPaidUser.email) : selectedOrderToPayUser.userId,
                // userEmail: toPaidUser ? (toPaidUser.email) : selectedOrderToPayUser.userId,
                // crmUserId: toPaidUser ? (toPaidUser.uniqueId) : '',
                // userPhone: toPaidUser ? (toPaidUser.number) : selectedOrderToPayUser.userPhone,
                userId: toPaidUser ? (toPaidUser.firebaseUid || toPaidUser.email) : ((selectedOrderToPayUser && selectedOrderToPayUser.userId) ? selectedOrderToPayUser.userId : ''),
                userEmail: toPaidUser ? (toPaidUser.email) : '',
                crmUserId: toPaidUser ? (toPaidUser.uniqueId) : '',
                userPhone: toPaidUser ? (toPaidUser.number) : (selectedOrderToPayUser && selectedOrderToPayUser.userPhone ? selectedOrderToPayUser.userPhone : ''),

                userIdAnonymous: selectedOrderToPayListTemp[0].userIdAnonymous ? selectedOrderToPayListTemp[0].userIdAnonymous : '',

                // promotionIdList: totalPromotionIdListTemp,
                promoCodePromotionIdList: totalPromoCodePromotionIdListTemp,
                cartPromotionIdList: selectedOrderToPayListTemp.map(order => order.promotionIdList || []).reduce((accum, idList) => accum.concat([...idList]), []),

                discAam: totalDiscAamTemp,
                discAamList: totalDiscAamListTemp,

                taggableVoucherIdList: totalTaggableVoucherIdListTemp,
                userTaggableVoucherIdList: totalUserTaggableVoucherIdListTemp,

                cartItems: [...selectedOrdersCartItemsTemp.map(cartItem => ({
                    ...cartItem,
                    // priceOriginal: cartItem.price,

                    priceOriginal: cartItem.priceOriginal || cartItem.price,
                }))],
                orderType: ORDER_TYPE.PICKUP,
                paymentMethod: 'Offline',
                userVoucherId: null,
                userVoucherCode: null,
                userAddressId: null,
                orderDate: selectedOrderToPayListTemp[0].orderDate,
                totalPrice: +totalPriceTemp.toFixed(2),

                discountVoucher: 0,
                discount: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discount || 0).toNumber();
                }, 0), // for all discounts
                discountPromotionsTotal: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discountPromotionsTotal || 0).toNumber();
                }, 0),

                discAam: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discAam || 0).toNumber();
                }, 0),
                discAamFinal: selectedOrderToPayListTemp.reduce((accum, curr) => {
                    return BigNumber(accum).plus(curr.discAam || 0).toNumber();
                }, 0),

                tax: +taxTemp.toFixed(2),
                sc: +scTemp.toFixed(2),
                deliveryFee: 0,

                totalPriceTaxList: totalPriceTaxList,

                // tableId: !isCheckingOutTakeaway
                //     ? selectedOutletTable.uniqueId
                //         ? selectedOutletTable.uniqueId
                //         : ''
                //     : '',
                // tablePax: !isCheckingOutTakeaway
                //     ? tablePaxTemp
                //         ? tablePaxTemp
                //         : 0
                //     : 0,
                // tableCode: !isCheckingOutTakeaway
                //     ? selectedOutletTable.code
                //         ? selectedOutletTable.code
                //         : ''
                //     : 0,
                tableId: '',
                tablePax: 0,
                tableCode: '',

                finalPrice: +finalPrice.toFixed(2),
                finalPriceBefore: +finalPriceBefore.toFixed(2),
                outletId: selectedOutlet.uniqueId,

                printDt: Date.now(), // to print receipt for pay later online

                merchantId: selectedOrderToPayListTemp[0].merchantId ? selectedOrderToPayListTemp[0].merchantId : '',
                outletCover: selectedOutlet ? selectedOutlet.cover : '',
                merchantLogo: selectedOrderToPayListTemp[0].merchantLogo ? selectedOrderToPayListTemp[0].merchantLogo : '',
                outletName: selectedOutlet ? selectedOutlet.name : '',
                merchantName: selectedOrderToPayListTemp[0].merchantName ? selectedOrderToPayListTemp[0].merchantName : '',

                outletAddress: selectedOutlet ? selectedOutlet.address : '',
                outletPhone: selectedOutlet ? selectedOutlet.phone : '',
                outletTaxId: '',
                outletTaxNumber: '',
                outletTaxName: '',
                // outletTaxRate: selectedOutlet
                //   ? outletsTaxDict[selectedOutlet.uniqueId].rate
                //   : 0.06,
                outletTaxRate: checkToApplyTaxOrNot(selectedOutlet, ORDER_TYPE.PICKUP) ? selectedOutlet.taxRate : 0,

                outletScRate:
                    checkToApplyScOrNot(selectedOutlet, ORDER_TYPE.PICKUP)
                        ? selectedOutlet.scRate
                        : 0,

                // orderStatus: userOrdersTableDict[selectedOutletTable.uniqueId], // this will store entire order as orderStatus
                // orderStatus: USER_ORDER_STATUS.ORDER_DELIVERED,
                orderStatus: USER_ORDER_STATUS.ORDER_PREPARED,

                courierCode: '',
                courierId: '',
                courierStatus: '',

                courierLink: '',
                driverId: '',
                driverDetails: null,

                scheduleAt: null,

                //////////////////////////////////////

                // append delivery info into user order itself, enable merchant later call courier again

                // cr stands for 'courier raw'

                crDeliveryCurrency: '',

                crOutletLat: null,
                crOutletLng: null,
                crOutletAddress: '',

                crOutletPhone: '',

                crUserLat: null,
                crUserLng: null,
                crUserAddress: '',

                crUserName: '',
                crUserPhone: '',
                crUserRemarks: '',

                crScheduleAt: '',

                crTotalWeightKg: null,
                crOutletRequiredStartDatetime: '',
                crOutletRequiredFinishDatetime: '',
                crUserRequiredStartDatetime: '',
                crUserRequiredFinishDatetime: '',

                //////////////////////////////////////

                isPrioritizedOrder: false,
                // waiterName: userName ? userName : '',
                waiterName: '',
                // waiterId: userId ? userId : '',
                waiterId: '',

                totalPrepareTime: totalPrepareTimeTemp,
                estimatedPreparedDate: estimatedPreparedDateTemp,

                remarks: remarksTemp,

                paymentDetails: null,

                ///////////////////////////////

                collectionDate: selectedOrderToPayListTemp[0].collectionDate,
                completedDate: null,

                paymentDate: null,

                priority: USER_ORDER_PRIORITY.NORMAL, // should use one of the joined order?

                userAddress: selectedOrderToPayUser && selectedOrderToPayUser.userAddress
                    ? selectedOrderToPayUser.userAddress
                    : '',
                // userName: selectedOrderToPayUser.userName
                //   ? selectedOrderToPayUser.userName
                //   : '',
                userName: toPaidUser ? (toPaidUser.name) : (selectedOrderToPayUser && selectedOrderToPayUser.userName ? selectedOrderToPayUser.userName : ''),
                // userPhone: selectedOrderToPayUser.userPhone
                //   ? selectedOrderToPayUser.userPhone
                //   : '',

                // orderId: nanoid(),
                receiptId: nanoid(),

                orderId: orderIdTemp,
                orderIdJoined: orderIdTemp,
                // receiptId: (outletOrderNumber.number + '').padStart(4, '0'),

                ///////////////////////////////

                preorderPackageId: null,
                preorderCollectionDate: null,
                preorderCollectionTime: null,

                ///////////////////////////////

                pointsToRedeem: 0,
                pointsToRedeemPackageIdList: [],
                pointsToRedeemAmountList: [],
                pointsToRedeemDiscount: 0,
                usePointsToRedeem: false,

                // splitAmountList: [], // for split bill

                tableQRUrl: selectedOrderToPayListTemp[0].tableQRUrl
                    ? selectedOrderToPayListTemp[0].tableQRUrl
                    : '',
                orderRegisterQRUrl: selectedOrderToPayListTemp[0].orderRegisterQRUrl
                    ? selectedOrderToPayListTemp[0].orderRegisterQRUrl
                    : '',

                ////////////////////

                deliveryPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.deliveryPackagingFee || 0, 0),
                pickupPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.pickupPackagingFee || 0, 0),

                // isReservationOrder: selectedOrderToPayListTemp[0].isReservationOrder ? selectedOrderToPayListTemp[0].isReservationOrder : false,

                // printedTokenList: [],
                printedTokenList: selectedOrderToPayListTemp.reduce((accum, order) => accum.concat(order.printedTokenList ? order.printedTokenList : []), []),

                isOnlineOrdering: selectedOrderToPayListTemp[0].isOnlineOrdering ? selectedOrderToPayListTemp[0].isOnlineOrdering : false,

                // appType: selectedOrderToPayListTemp[0].appType ? selectedOrderToPayListTemp[0].appType : APP_TYPE.WEB_ORDER,
                appType: APP_TYPE.WEB_ORDER,

                settlementDate: null,

                scName: selectedOutlet.scName ? selectedOutlet.scName : '',

                ////////////////////

                createdAt: moment().valueOf(),
                updatedAt: moment().valueOf(),
                deletedAt: null,
            };

            if (isNaN(currPendingOrderTemp.finalPrice)) {
                window.confirm('Invalid order(s) to proceed.');
            }
            else {
                // setCurrPendingOrderDiscount(0);
                // setCurrPendingOrder(currPendingOrderTemp);
                // setSelectedOrderToPayList(selectedOrderToPayListTemp);
                // setSelectedOrdersCartItems(selectedOrdersCartItemsTemp);
                // setRenderPaymentSummary(true);

                TableStore.update(s => {
                    s.currPendingOrderDiscount = 0;
                    s.currPendingOrder = currPendingOrderTemp;
                    s.selectedOrderToPayList = selectedOrderToPayListTemp;
                    s.selectedOrdersCartItems = selectedOrdersCartItemsTemp;
                    s.renderPaymentSummary = true;

                    // 2022-11-17 - changes
                    s.viewTableOrderModal = false;
                });

                var payHybridBodyTemp = {
                    orders: JSON.parse(JSON.prune(selectedOrderToPayListTemp)),
                    ordersCartItems: JSON.parse(JSON.prune(selectedOrdersCartItemsTemp)),
                    destOrder: {
                        ...currPendingOrderTemp,
                        discountPromotionsTotalLCC: 0,
                        // promotionIdList: promotionIdAppliedList,
                        promotionIdList: [],
                        promoCodePromotionIdList: [
                            ...new Set(
                                (currPendingOrderTemp.promoCodePromotionIdList ? currPendingOrderTemp.promoCodePromotionIdList : [])
                                // .filter(promotionId => {
                                //     if (availablePromoCodePromotions.find(promoCodePromotion => promoCodePromotion.uniqueId === promotionId)) {
                                //         return true;
                                //     }
                                //     else {
                                //         return false;
                                //     }
                                // })
                            )
                        ],

                        ...(selectedOutlet.multiTax) && { multiTax: selectedOutlet.multiTax },

                        taggableVoucherIdList: totalTaggableVoucherIdListTemp,
                        userTaggableVoucherIdList: totalUserTaggableVoucherIdListTemp,

                        loyaltyCampaignId: null,

                        taggableVoucherId: null,
                        userTaggableVoucherId: null,

                        cartItems: currPendingOrderTemp.cartItems,

                        discount: currPendingOrderTemp.discount,

                        totalPrice: currPendingOrderTemp.totalPrice,
                        tax: currPendingOrderTemp.tax,
                        sc: currPendingOrderTemp.sc,

                        totalPriceTaxList: currPendingOrderTemp.totalPriceTaxList ? currPendingOrderTemp.totalPriceTaxList : [{
                            key: '',
                            totalPrice: totalPriceTemp,
                            tax: 0,
                            tRate: 0,
                            tCode: '',
                            tName: 'SST',
                        }],
                    },
                    billType: OFFLINE_BILL_TYPE.SUMMARY,
                    paymentMethod: 'Offline-Cash',
                    paymentMethodRemarks: '',
                    amountReceived: currPendingOrderTemp.finalPrice,
                    amountBalance: 0,

                    outletData: selectedOutlet,
                };

                CommonStore.update(s => {
                    s.payHybridBody = payHybridBodyTemp;

                    s.orderType = ORDER_TYPE.PICKUP;
                });

                const subdomain = await AsyncStorage.getItem("latestSubdomain");

                if (subdomain === 'mykoodoo') {
                    linkTo(`${prefix}/outlet/pay-cart`);
                }
                else {
                    linkTo(`${prefix}/outlet/${subdomain}/pay-cart`);
                }
            }
        }
    };

    // function end

    var detailsFontSize = 20;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale = {
        fontSize: detailsFontSize,
    };

    var detailsFontSize2 = 16;

    if (Dimensions.get('screen').width <= 360) {
        detailsFontSize2 = 13;
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTextScale2 = {
        fontSize: detailsFontSize2,
    };

    var detailsTopSpace = '48%';

    if (Dimensions.get('screen').width <= 360) {
        detailsTopSpace = '56.5%';
        //console.log(Dimensions.get('screen').width)
    }

    const detailsTopSpacing = {
        top: detailsTopSpace,
    };

    const waitForSender = () => {
        const body = {
            orderId: selectedUserOrderTakeaway.uniqueId,
        };

        ApiClient.POST(API.waitForSender, body).then(async (result) => {
            // Alert.alert(
            //     'Success',
            //     'The user of this order had been notified.',
            //     [{ text: 'OK', onPress: () => { } }],
            //     { cancelable: false },
            // );

            // const userOrderSnapshot = await firebase().firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrderTakeaway = userOrder;
            }, () => {
                setActionModalVisibility(false);
            });
        });
    }

    // const getTestData = async () => {
    //     const userOrderSnapshot = await firebase.firestore().collection(Collections.UserOrder)

    //         .where('uniqueId', '==', orderId)
    //         .limit(1)
    //         .get();

    //     var userOrder = null;

    //     if (!userOrderSnapshot.empty) {
    //         userOrder = userOrderSnapshot.docs[0].data();
    //     }

    //     CommonStore.update(s => {
    //         s.selectedUserOrderTakeaway = userOrder;
    //     });
    // };

    // useEffect(() => {
    //     getTestData()
    // }, [])

    useEffect(() => {
        global.currPageStack = [
            ...global.currPageStack,
            'OrderHistoryDetailTakeaway',
        ];
    }, []);

    const startMolPay = () => {
        // need get readable order id from api first

        const body = {
            outletId: selectedUserOrderTakeaway.outletId,
        };

        var amountToPay = Math.max(deliveryQuotation.totalFee - selectedUserOrderTakeaway.deliveryFee, 0);

        // Testing

        amountToPay = amountToPay > 5 ? 5 : amountToPay;

        var paymentDetails = {
            // Optional, REQUIRED when use online Sandbox environment and account credentials.
            'mp_dev_mode': true,

            // Mandatory String. Values obtained from Razer Merchant Services.
            'mp_username': 'api_SB_mykoodoo',
            'mp_password': 'WaaU1IeZ*&(%%',
            'mp_merchant_ID': 'SB_mykoodoo',
            'mp_app_name': 'mykoodoo',
            'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

            // Mandatory String. Payment values.
            'mp_amount': amountToPay, // Minimum 1.01
            'mp_order_ID': `#${selectedUserOrderTakeaway.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${selectedUserOrderTakeaway.orderId}`,
            'mp_currency': 'MYR',
            'mp_country': 'MY',

            // Optional String.
            'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
            'mp_bill_description': selectedUserOrderTakeaway.outletName + ' delivery topup RM' + parseFloat(amountToPay).toFixed(2),
            'mp_bill_name': selectedUserOrderTakeaway.userName,
            'mp_bill_email': userEmail,
            'mp_bill_mobile': selectedUserOrderTakeaway.userPhone,

            'mp_sandbox_mode': true,
            'mp_tcctype': 'AUTH',
        }

        window.startMolpay(paymentDetails, (data) => {
            //callback after payment success

            console.log("razer result v2");
            console.log(data);

            const result = JSON.parse(data);
            console.log(result);

            if (result.error_code || result.Error) {
                console.log('razer error');

                Alert.alert(
                    "Error",
                    "Failed to process your payment",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                                // navigation.jumpTo('Home')
                                // navigation.navigate('Home');
                                navigation.dispatch(
                                    CommonActions.reset({
                                        index: 0,
                                        routes: [{ name: "Home" }]
                                    }));
                            },
                        },
                    ],
                    { cancelable: false }
                );

                CommonStore.update(s => {
                    s.isLoading = false;
                });
            }
            else {
                console.log("payment success v2");

                topupUpdateCourier(result);
            }

            // placeUserOrder();
        });
    }

    const topup = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        // think should record down the topup payment details

        const deliveryFeeDiff = Math.max(deliveryQuotation.totalFee - selectedUserOrderTakeaway.deliveryFee, 0);

        if (deliveryFeeDiff > 0) {
            startMolPay();
        }
        else {
            topupUpdateCourier();
        }
    }

    const topupUpdateCourier = (paymentDetails) => {
        var body = {
            orderId: selectedUserOrderTakeaway.uniqueId,
            merchantId: selectedUserOrderTakeaway.merchantId,

            deliveryFeeDiff: Math.max(deliveryQuotation.totalFee - selectedUserOrderTakeaway.deliveryFee, 0),

            deliveryFee: deliveryQuotation.totalFee,
            courierCode: deliveryQuotation.courierCode,
            deliveryCurrency: deliveryQuotation.totalFeeCurrency,

            paymentDetailsCourierAction: paymentDetails ? paymentDetails : null,
        };

        ApiClient.POST(API.updateUserOrderCourierByUser, body, {
            timeout: 10000,
        }).then(async (result) => {
            console.log("updateUserOrderCourierByUser");
            console.log(result)

            if (result) {
                if (result.status === 'success') {
                    Alert.alert(
                        "Success",
                        "Sender for this order changed successfully.",
                        [
                            {
                                text: "OK",
                                onPress: async () => {
                                    // const userOrderSnapshot = await firebase.firestore()
                                    //     .collection(Collections.UserOrder)
                                    //     .where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId)
                                    //     .limit(1)
                                    //     .get();

                                    const userOrderSnapshot = await getDocs(
                                        query(
                                            collection(global.db, Collections.UserOrder),
                                            where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId),
                                            limit(1),
                                        )
                                    );

                                    var userOrder = null;
                                    if (!userOrderSnapshot.empty) {
                                        userOrder = userOrderSnapshot.docs[0].data();
                                    }

                                    CommonStore.update(s => {
                                        s.selectedUserOrderTakeaway = userOrder;
                                    }, () => {
                                        setActionModalVisibility(false);
                                    });
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
                else {
                    Alert.alert(
                        "Error",
                        result.message,
                        [
                            {
                                text: "OK",
                                onPress: () => {
                                },
                            },
                        ],
                        { cancelable: false }
                    );
                }
            }
            else {
                Alert.alert(
                    "Error",
                    "Failed to change the sender for this order.",
                    [
                        {
                            text: "OK",
                            onPress: () => {
                            },
                        },
                    ],
                    { cancelable: false }
                );
            }

            CommonStore.update(s => {
                s.isLoading = false;
            });
        });
    };

    const completeUserOrderByUser = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrderTakeaway.uniqueId,
        };

        ApiClient.POST(API.completeUserOrderByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrderTakeaway = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(true);
            });
        });
    }

    const submitReview = () => {
        CommonStore.update(s => {
            s.isLoading = true;
        });

        const body = {
            orderId: selectedUserOrderTakeaway.uniqueId,
            userId: selectedUserOrderTakeaway.userId,
            userName: userName,
            userImage: userAvatar,
            outletId: selectedUserOrderTakeaway.outletId,
            outletName: selectedUserOrderTakeaway.outletName,
            merchantId: selectedUserOrderTakeaway.merchantId,
            merchantName: selectedUserOrderTakeaway.merchantName,
            ratings: starRatingDefault,
            comments: reviewComment,
        };

        console.log(body);

        ApiClient.POST(API.submitOrderReviewByUser, body).then(async (result) => {
            // const userOrderSnapshot = await firebase.firestore()
            //     .collection(Collections.UserOrder)
            //     .where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId)
            //     .limit(1)
            //     .get();

            const userOrderSnapshot = await getDocs(
                query(
                    collection(global.db, Collections.UserOrder),
                    where('uniqueId', '==', selectedUserOrderTakeaway.uniqueId),
                    limit(1),
                )
            );

            var userOrder = null;
            if (!userOrderSnapshot.empty) {
                userOrder = userOrderSnapshot.docs[0].data();
            }

            CommonStore.update(s => {
                s.selectedUserOrderTakeaway = userOrder;

                s.isLoading = false;
            }, () => {
                // setActionModalVisibility(false);

                setCompleteModalVisibility(false);
            });
        });
    };

    //const starFilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_filled.png'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    //const starFilled = 'https://lh3.googleusercontent.com/proxy/DhwXgAC-E6rS9vYy3DIgsv8d9_G7b7hGCi5Aa3-LF0rCnWE3yhGoAoM_2ucnL-BEAHtWhAq-RHyho_LnDskAXBA0jA5OlCuISQ'
    //const starUnfilled = 'https://raw.githubusercontent.com/tranhonghan/images/main/star_corner.png'

    const StarRatingView = () => {

        return (
            <>
                {
                    starRating.map((item, key) => {
                        return (
                            <>
                                <TouchableOpacity
                                    style={{ marginLeft: 3 }}
                                    key={item}
                                    onPress={() =>
                                        setStarRatingDefault(item)
                                    }
                                >
                                    {/* <Image
                    style={styles.starStyle}
                    source={
                        item <= starRatingDefault
                        ? {uri: starFilled}
                        : {uri: starUnfilled}
                    }
                    /> */}
                                    <AntDesign name={'star'} size={35}
                                        color={
                                            item <=
                                                starRatingDefault
                                                ? Colors.primaryColor
                                                : Colors.fieldtTxtColor
                                        } />
                                </TouchableOpacity>
                            </>
                        )
                    }
                    )
                }
            </>
        )
    }


    const renderQuestionnaire = ({ item, index }) => (
        <>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                        setQuestionnaireVisibility(false);
                    }}>
                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                </TouchableOpacity>
            </View>
            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                <Text style={{
                    fontSize: 20,
                    fontFamily: "NunitoSans-Bold"
                }}>
                    Questionnaire
                </Text>
                <Text style={{
                    fontSize: 13,
                    fontFamily: "NunitoSans-Regular",
                    marginTop: 5
                }}>
                    Help us serve you better
                </Text>
            </View>

            <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you a vegetarian?
            </Text>

            <Picker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0, }}
                dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
                arrowSize={17}
                arrowColor={'black'}
                arrowStyle={{ paddingVertical: 0 }}
                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                placeholderStyle={{ marginLeft: 0 }}
                placeholder={" Answer"}
                items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

            />

            {/* <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Are you allergic to any food or ingredient?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        />

        
        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                What foods or ingredients are you allergic to?
        </Text>
        <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            clearButtonMode='while-editing'
            placeholder={"Answer"}
            style={styles.questionInput}
            />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like fast food or healthy food?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Fast Food', value: 'Fast Food' }, { label: 'Healthy Food', value: 'Healthy Food' }]}

        />

        <Text style={{
                fontSize: 13,
                fontFamily: "NunitoSans-Regular",
                marginTop: 10
            }}>
                Do you like dessert?
        </Text>
        <DropDownPicker style={{ marginTop: 5, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 30, width: 200, paddingVertical: 0,  }}
            
            dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, marginHorizontal: 10 }}
            arrowSize={17}
            arrowColor={'black'}
            arrowStyle={{ paddingVertical: 0 }}
            itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
            placeholderStyle={{ marginLeft: 0 }}
            placeholder={" Answer"}
            items={[{ label: 'Yes', value: 'Yes' }, { label: 'No', value: 'No' }]}

        /> */}
            {expandCompleteQuestionnaire ? (
                <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                    <Text style={{
                        fontSize: 20,
                        fontFamily: "NunitoSans-Bold"
                    }}>
                        Thank you !
                    </Text>
                    <Text style={{
                        fontSize: 13,
                        fontFamily: "NunitoSans-Regular",
                        marginTop: 5
                    }}>
                        Hope to see you again
                    </Text>
                </View>
            ) : null}
            <View style={{ flexDirection: 'row', marginTop: 25, justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10, }}>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                    marginRight: 10
                }}
                    onPress={() => {
                        setExpandCompleteQuestionnaire(true);
                    }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>

                </TouchableOpacity>
                <TouchableOpacity style={{
                    width: "40%",
                    height: 40,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    borderWidth: 1,
                    borderColor: Colors.descriptionColor,
                    zIndex: 1000,
                }}
                >

                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>NEXT</Text>

                </TouchableOpacity>
            </View>
        </>
    );

    var orderCodeFont = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont = 14;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize = {
        fontSize: orderCodeFont,
    };

    var orderCodeFont1 = 16;

    if (Dimensions.get('screen').width <= 360) {
        orderCodeFont1 = 12;
        //console.log(Dimensions.get('screen').width)
    }

    const orderCodeFontSize1 = {
        fontSize: orderCodeFont1,
    };

    const proceedToReceiptDetails = async (item) => {
        // const outletSnapshot = await firebase().collection(Collections.Outlet)
        //     .firestore
        //     .where('uniqueId', '==', item.outletId)
        //     .limit(1)
        //     .get();

        const outletSnapshot = await getDocs(
            query(
                collection(global.db, Collections.Outlet),
                where('uniqueId', '==', item.outletId),
                limit(1),
            )
        );

        var outlet = null;

        if (!outletSnapshot.empty) {
            outlet = outletSnapshot.docs[0].data();
        }

        CommonStore.update(s => {
            s.selectedUserOrderTakeaway = item;
            s.selectedOutlet = outlet;
        });

        var tempUserAddress = null;
        for (var i = 0; i < userAddresses.length; i++) {
            if (userAddresses[i].uniqueId === item.userAddressId) {
                tempUserAddress = userAddresses[i];
            }
        }

        props.navigation.navigate('ReceiptDetail', {
            orderId: item.uniqueId,
            order: item,
            outlet: outlet,

            userDetails: {
                address: tempUserAddress.address,
                name: userName,
                phone: userNumber,
                email: userEmail,
                userId: firebaseUid,
            },
        });
    }

    const forcePayAtCashier = CommonStore.useState(s => s.forcePayAtCashier);
    const unpaidOrder = useMemo(() => {
        return toRenderOrders.filter(order =>
            !(order.paymentDetails && order.paymentDetails.channel)
        );
    }, [toRenderOrders]);

    return (
        <View style={{ height: windowHeight * 0.925 }}>
            {
                (orderToShow && orderToShow.uniqueId)
                    ?
                    <>
                        <ScrollView style={styles.container} ref={scrollViewRef} showsVerticalScrollIndicator={false} contentContainerStyle={{
                            paddingBottom: 40,
                        }}>
                            {(forcePayAtCashier && unpaidOrder.length > 0) && (
                                <View style={{
                                    position: 'sticky',
                                    top: 0,
                                    backgroundColor: Colors.safetyOrange,
                                    paddingVertical: 5,
                                    zIndex: 1000,
                                }}>
                                    <Text style={{
                                        textAlign: 'center',
                                        color: Colors.whiteColor,
                                        fontFamily: "NunitoSans-SemiBold",
                                        fontSize: 16,
                                    }}>Please pay at cashier to confirm and complete order</Text>
                                </View>
                            )}

                            <View style={{ padding: 10 }}>
                                <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                                    <View style={{ alignSelf: 'center' }}>
                                        <AsyncImage

                                            source={{ uri: orderToShow ? orderToShow.merchantLogo : '' }}
                                            // item={selectedUserOrderTakeaway}
                                            style={{ width: 90, height: 90, borderRadius: 10 }}
                                        />
                                    </View>
                                </View>
                                <View style={styles.titleDetail}>
                                    <View style={{ flexDirection: 'row' }}>
                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 18 }}>{orderToShow ? orderToShow.outletName : ''}</Text>
                                        {/* <TouchableOpacity
                    style={{
                        marginLeft: 10
                    }}
                    onPress={() => {

                        CommonStore.update(s => {
                            s.selectedUserOrderTakeaway = selectedUserOrderTakeaway;
                          });

                        navigation.navigate('ReceiptDetail');
                        proceedToReceiptDetails(selectedUserOrderTakeaway);
                     }}>
                    <Ionicons name={'receipt-outline'} size={30} color={Colors.primaryColor}/>
                    </TouchableOpacity> */}
                                    </View>
                                    <Text
                                        style={{
                                            marginTop: 10,
                                            textAlign: 'center',
                                            fontFamily: "NunitoSans-Bold",
                                        }}>
                                        {orderOutlet.address}
                                    </Text>
                                    <Text style={{
                                        marginTop: 0, fontFamily: "NunitoSans-Bold",
                                    }}>
                                        Phone: {orderOutlet.phone}
                                    </Text>
                                </View>

                                <View style={[styles.orderTitle]}>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row', width: '50%' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Order # :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 5,
                                                width: '50%',
                                            }]} numberOfLines={2}>
                                                {orderToShow ? orderToShow.orderId : ''}
                                                {selectedUserOrderOthersTakeaway.length > 0 ? `, ${(selectedUserOrderOthersTakeaway.map(order => order.orderId).join(', '))}` : ''}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Order Type :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-Bold",
                                                marginBottom: 5,
                                                /* width: '70%' */
                                            }]} numberOfLines={2}>
                                                {ORDER_TYPE_PARSED[orderToShow ? orderToShow.orderType : '']}
                                            </Text>
                                        </View>
                                    </View>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Date :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {moment(orderToShow ? orderToShow.orderDate : '').format('DD-MMM-YYYY')}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                Time :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 5,
                                            }]}>
                                                {moment(orderToShow ? orderToShow.orderDate : '').format('hh:mm A')}
                                            </Text>
                                        </View>
                                    </View>

                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                Name :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                {userInfoName ? userInfoName : 'N/A'}
                                            </Text>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                Phone :{' '}
                                            </Text>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 10,
                                            }]}>
                                                {userInfoPhone ? userInfoPhone : 'N/A'}
                                            </Text>
                                        </View>
                                    </View>
                                </View>

                                <FlatList
                                    removeClippedSubviews={true}
                                    style={{
                                        // width: '100%',
                                        // minHeight: windowHeight * 0.05,
                                    }}
                                    contentContainerStyle={{
                                        // paddingHorizontal: 15,
                                    }}
                                    data={
                                        toRenderOrders.reduce(
                                            (accu, order) =>
                                                accu.concat(
                                                    order.cartItems
                                                        // .filter(
                                                        //     (cartItem) =>
                                                        //         cartItem.deliveredAt === null,
                                                        // )
                                                        .map(cartItem => ({
                                                            ...cartItem,
                                                            userOrderId: order.uniqueId,

                                                            userName: order.userName ? order.userName : 'N/A',
                                                            orderId: order.orderId,
                                                        })),
                                                ),
                                            [],
                                        )
                                    }
                                    renderItem={renderTableOrder}
                                    keyExtractor={(item, index) => String(index)}
                                />

                                {/* 2024-08-21 - for pending approval takeaway orders */}

                                {
                                    pendingApprovalOrders.length > 0
                                        ?
                                        <View style={{
                                            flexDirection: 'column',

                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: Colors.fieldtTxtColor,

                                            paddingTop: 5,
                                        }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 2,
                                            }]}>
                                                Orders Pending Approval
                                            </Text>

                                            <FlatList
                                                removeClippedSubviews={true}
                                                style={{
                                                    // width: '100%',
                                                    // minHeight: windowHeight * 0.05,
                                                }}
                                                contentContainerStyle={{
                                                    // paddingHorizontal: 15,
                                                }}
                                                data={
                                                    pendingApprovalOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                order.cartItems
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                }
                                                renderItem={renderTableOrder}
                                                keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>
                                        :
                                        <></>
                                }

                                {/* 2023-02-23 - Show cancelled items */}

                                {
                                    toRenderOrders.find(order => {
                                        if (order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
                                            return true;
                                        }
                                        else {
                                            return false;
                                        }
                                    })
                                        ?
                                        <View style={{
                                            flexDirection: 'column',

                                            borderTopWidth: StyleSheet.hairlineWidth,
                                            borderTopColor: Colors.fieldtTxtColor,

                                            paddingTop: 5,
                                        }}>
                                            <Text style={[orderCodeFontSize, {
                                                fontFamily: "NunitoSans-SemiBold",
                                                marginBottom: 2,
                                            }]}>
                                                Rejected Items
                                            </Text>

                                            <FlatList
                                                removeClippedSubviews={true}
                                                style={{
                                                    // width: '100%',
                                                    // minHeight: windowHeight * 0.05,

                                                }}
                                                contentContainerStyle={{
                                                    // paddingHorizontal: 15,
                                                }}
                                                data={
                                                    toRenderOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                (order.cartItemsCancelled ? order.cartItemsCancelled : [])
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,

                                                                        isCancelledItem: true,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                }
                                                renderItem={renderTableOrder}
                                                keyExtractor={(item, index) => String(index)}
                                            />
                                        </View>
                                        :
                                        <></>
                                }

                                {/* {selectedUserOrderTakeaway && selectedUserOrderTakeaway.cartItems != null
                                ? selectedUserOrderTakeaway.cartItems.map((element, index) => {
                                    return (
                                        <View>
                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                <View style={{ width: "60%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                </View>
                                                <View style={{ width: "10%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                </View>
                                                <View style={{ width: "5%" }}>
                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                </View>
                                                <View style={{ width: "25%" }}>
                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                        <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                                    </View>
                                                </View>
                                            </View>

                                            {element.remarks && element.remarks.length > 0 ?
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <View style={{ width: "60%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                    </View>
                                                    <View style={{ width: "10%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                    <View style={{ width: "5%" }}>
                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                    <View style={{ width: "25%" }}>
                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                    </View>
                                                </View>
                                                : <></>
                                            }

                                            {element.addOns.map((addOnChoice, i) => {
                                                return (
                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                        <View style={{ width: "60%", }}>
                                                            <View style={{ flexDirection: 'row', }}>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                            </View>
                                                        </View>
                                                        <View style={{ width: "10%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                                ? `x${addOnChoice.quantities[0]}`
                                                                : ''
                                                                }`}</Text>
                                                        </View>
                                                        <View style={{ width: "5%" }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                                        </View>
                                                        <View style={{ width: "25%" }}>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                )
                                            })}

                                        </View>
                                    );
                                })
                                : null}

                            {
                                selectedUserOrderOthersTakeaway.map(order => {
                                    return (
                                        <>
                                            {order && order.cartItems != null
                                                ? order.cartItems.map((element, index) => {
                                                    return (
                                                        <View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                <View style={{ width: "60%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                                </View>
                                                                <View style={{ width: "10%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                                </View>
                                                                <View style={{ width: "5%" }}>
                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                </View>
                                                                <View style={{ width: "25%" }}>
                                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                        <Text style={{ fontSize: 10, alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", }}>RM </Text>
                                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{(element.price + (element.discountPromotions ? element.discountPromotions : 0)).toFixed(2)}</Text>
                                                                    </View>
                                                                </View>
                                                            </View>

                                                            {element.remarks && element.remarks.length > 0 ?
                                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                    <View style={{ width: "60%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                                    </View>
                                                                    <View style={{ width: "10%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                    <View style={{ width: "5%" }}>
                                                                        <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                    <View style={{ width: "25%" }}>
                                                                        <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, }}></Text>
                                                                    </View>
                                                                </View>
                                                                : <></>
                                                            }

                                                            {element.addOns.map((addOnChoice, i) => {
                                                                return (
                                                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                        <View style={{ width: "60%", }}>
                                                                            <View style={{ flexDirection: 'row', }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '30%' }}>{`${addOnChoice.name}:`}</Text>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, width: '70%' }}>{`${addOnChoice.choiceNames[0]}`}</Text>
                                                                            </View>
                                                                        </View>
                                                                        <View style={{ width: "10%" }}>
                                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{`${addOnChoice.quantities
                                                                                ? `x${addOnChoice.quantities[0]}`
                                                                                : ''
                                                                                }`}</Text>
                                                                        </View>
                                                                        <View style={{ width: "5%" }}>
                                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}></Text>
                                                                        </View>
                                                                        <View style={{ width: "25%" }}>
                                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>RM </Text>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10, color: Colors.descriptionColor, }}>{addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(2)}</Text>
                                                                            </View>
                                                                        </View>
                                                                    </View>
                                                                )
                                                            })}

                                                        </View>
                                                    );
                                                })
                                                : null}
                                        </>
                                    );
                                })
                            } */}

                                <View style={styles.totalContainer}>
                                    <View style={{ flex: 1, width: '75%', paddingTop: 10, }}>
                                        <Text style={styles.description}>Subtotal</Text>
                                        <Text style={styles.description}>Discount (-)</Text>
                                        {/* <Text style={styles.description}> */}
                                        {/* Tax ( */}
                                        {/* {orderTax ? orderTax.rate : 0} */}
                                        {/* {orderOutlet.taxPercent} */}
                                        {/* %) */}
                                        {/* </Text> */}
                                        {
                                            selectedOutlet && selectedOutlet.taxActive
                                                ? (
                                                    orderToShow && orderToShow.totalPriceTaxList && orderToShow.totalPriceTaxList.length > 0
                                                        ? (
                                                            orderToShow.totalPriceTaxList.map((taxItem, index) => (
                                                                <Text
                                                                    key={index}
                                                                    style={[styles.description]}>
                                                                    {taxItem.tName
                                                                        ? `${taxItem.tName} (${(taxItem.tRate * 100).toFixed(0)}%)`
                                                                        : `Tax (${(taxItem.tRate * 100).toFixed(0)}%)`}
                                                                </Text>
                                                            ))
                                                        ) : (
                                                            <Text
                                                                style={[styles.description]}>
                                                                {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%) ${selectedOutlet.taxNum ? `(${selectedOutlet.taxNum})` : ''}`}
                                                            </Text>
                                                        )
                                                )
                                                :
                                                <></>
                                        }
                                        {
                                            selectedOutlet && selectedOutlet.scActive
                                                ?
                                                <Text
                                                    style={[
                                                        styles.description,
                                                    ]}>
                                                    {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                                </Text>
                                                :
                                                <></>
                                        }
                                        {/* <Text style={styles.description}>
                            Delivery Fee
                        </Text>
                        {
                            (selectedUserOrderTakeaway && selectedUserOrderTakeaway.deliveryPackagingFee && selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <Text style={styles.description}>
                                    Delivery Packaging Fee
                                </Text>
                                :
                                <></>
                        } */}

                                        {
                                            (orderToShow && orderToShow.pickupPackagingFee && orderToShow.orderType === ORDER_TYPE.PICKUP)
                                                ?
                                                <Text style={styles.description}>
                                                    Takeaway Packaging Fee
                                                </Text>
                                                :
                                                <></>
                                        }

                                        <Text
                                            style={[
                                                styles.description,
                                            ]}>
                                            {`Roundings`}
                                        </Text>

                                        <Text style={styles.total}>Total</Text>
                                    </View>
                                    <View style={{ alignSelf: 'flex-end', width: '25%', }}>
                                        {/* <Text style={styles.price}>RM {selectedUserOrderTakeaway.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            {orderToShow ?
                                                <Text style={styles.price}>{
                                                    (
                                                        orderToShow.totalPrice +
                                                        orderToShow.cartItems.reduce(
                                                            (accumCartItem, cartItem) =>
                                                                accumCartItem +
                                                                (cartItem.discount ? cartItem.discount : 0), 0
                                                        ) +
                                                        (orderToShow.discAam ? orderToShow.discAam : 0) +
                                                        (selectedUserOrderOthersTakeaway.reduce(
                                                            (accum, order) => accum +
                                                                (order.totalPrice
                                                                    +
                                                                    // order.discount +
                                                                    // (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                                    order.cartItems.reduce(
                                                                        (accumCartItem, cartItem) =>
                                                                            accumCartItem +
                                                                            (cartItem.discount ? cartItem.discount : 0), 0
                                                                    )
                                                                    +
                                                                    (order.discAam ? order.discAam : 0)
                                                                ),
                                                            0
                                                        )
                                                        )
                                                        // selectedUserOrderTakeaway.totalPrice +
                                                        // selectedUserOrderTakeaway.discount +
                                                        // (selectedUserOrderTakeaway.discountPromotionsTotal ? selectedUserOrderTakeaway.discountPromotionsTotal : 0) +
                                                        // (selectedUserOrderOthersTakeaway.reduce(
                                                        //     (accum, order) => accum +
                                                        //         (order.totalPrice +
                                                        //             order.discount +
                                                        //             (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                        // )
                                                    ).toFixed(2)
                                                }</Text> : <></>}
                                        </View>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            {orderToShow ? <Text style={styles.price}>{(
                                                // selectedUserOrderTakeaway.discount +
                                                //     selectedUserOrderTakeaway.discountPromotionsTotal ? selectedUserOrderTakeaway.discountPromotionsTotal :
                                                //     selectedUserOrderTakeaway.discount +
                                                //         selectedUserOrderTakeaway.preorderPackagePrice ? selectedUserOrderTakeaway.totalPrice - selectedUserOrderTakeaway.preorderPackagePrice :
                                                //         0
                                                orderToShow.cartItems.reduce(
                                                    (accumCartItem, cartItem) =>
                                                        accumCartItem +
                                                        (cartItem.discount ? cartItem.discount : 0), 0
                                                ) +
                                                (orderToShow.discAam ? orderToShow.discAam : 0) +
                                                (selectedUserOrderOthersTakeaway.reduce(
                                                    (accum, order) => accum +
                                                        // order.discount +
                                                        // (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0)), 0)
                                                        order.cartItems.reduce(
                                                            (accumCartItem, cartItem) =>
                                                                accumCartItem +
                                                                (cartItem.discount ? cartItem.discount : 0), 0
                                                        ) +
                                                        (order.discAam ? order.discAam : 0),
                                                    0
                                                )
                                                )
                                                // selectedUserOrderTakeaway.discount +
                                                // (selectedUserOrderTakeaway.discountPromotionsTotal ? selectedUserOrderTakeaway.discountPromotionsTotal : 0) +
                                                // (selectedUserOrderTakeaway.preorderPackagePrice ? selectedUserOrderTakeaway.totalPrice - selectedUserOrderTakeaway.preorderPackagePrice : 0) +

                                                // (selectedUserOrderOthersTakeaway.reduce((accum, order) => accum +
                                                //     (order.discount +
                                                //         (order.discountPromotionsTotal ? order.discountPromotionsTotal : 0) +
                                                //         (order.preorderPackagePrice ? order.totalPrice - order.preorderPackagePrice : 0)),
                                                //     0)
                                                // )
                                            ).toFixed(2)}</Text> : <></>}
                                        </View>
                                        {
                                            selectedOutlet && selectedOutlet.taxActive
                                                ? (
                                                    orderToShow && orderToShow.totalPriceTaxList && orderToShow.totalPriceTaxList.length > 0
                                                        ? (
                                                            orderToShow.totalPriceTaxList.map((taxItem, index) => (
                                                                <View key={index} style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                    <Text style={styles.price}>RM </Text>
                                                                    <Text style={styles.price}>{(taxItem.tax || 0).toFixed(2)}</Text>
                                                                </View>
                                                            ))
                                                        ) : (
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.price}>RM </Text>
                                                                <Text style={styles.price}>{
                                                                    (
                                                                        ((orderToShow && orderToShow.tax) ? orderToShow.tax : 0) +
                                                                        (selectedUserOrderOthersTakeaway.reduce((accum, order) => (accum + (order && order.tax) ? order.tax : 0), 0))
                                                                    ).toFixed(2)
                                                                }</Text>
                                                            </View>
                                                        )
                                                )
                                                :
                                                <></>
                                        }
                                        {
                                            selectedOutlet && selectedOutlet.scActive
                                                ?
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={styles.price}>RM </Text>
                                                    <Text style={styles.price}>{
                                                        // ((selectedUserOrderTakeaway && selectedUserOrderTakeaway.sc) ? selectedUserOrderTakeaway.sc.toFixed(2) : (0).toFixed(2))
                                                        (
                                                            ((orderToShow && orderToShow.sc) ? orderToShow.sc : 0) +
                                                            (selectedUserOrderOthersTakeaway.reduce((accum, order) => (accum + (order && order.sc) ? order.sc : 0), 0))
                                                        ).toFixed(2)
                                                    }</Text>
                                                </View>
                                                :
                                                <></>
                                        }
                                        {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={styles.price}>RM </Text>                            
                            {selectedUserOrderTakeaway ? <Text style={styles.price}>{(selectedUserOrderTakeaway && selectedUserOrderTakeaway.deliveryFee) ? selectedUserOrderTakeaway.deliveryFee.toFixed(2) : (0).toFixed(2)}</Text> : <></>}
                        </View>
                        {
                            (selectedUserOrderTakeaway && selectedUserOrderTakeaway.deliveryPackagingFee && selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY)
                                ?
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                    <Text style={styles.price}>RM </Text>
                                    <Text style={styles.price}>{selectedUserOrderTakeaway.deliveryPackagingFee.toFixed(2)}</Text>

                                </View>
                                :
                                <></>
                        } */}

                                        {
                                            (orderToShow && orderToShow.pickupPackagingFee && orderToShow.orderType === ORDER_TYPE.PICKUP)
                                                ?
                                                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                    <Text style={styles.price}>RM </Text>
                                                    <Text style={styles.price}>{orderToShow.pickupPackagingFee.toFixed(2)}</Text>
                                                </View>
                                                :
                                                <></>
                                        }

                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                            <Text style={styles.price}>RM </Text>
                                            <Text style={styles.price}>{
                                                (
                                                    (
                                                        (
                                                            (orderToShow && orderToShow.finalPrice) ? orderToShow.finalPrice : 0
                                                        ) +
                                                        (selectedUserOrderOthersTakeaway.reduce((accum, order) =>
                                                            accum + ((order && order.finalPrice) ? order.finalPrice : 0), 0)
                                                        )
                                                    )
                                                    -
                                                    (
                                                        (
                                                            (orderToShow && orderToShow.finalPriceBefore) ? orderToShow.finalPriceBefore : 0
                                                        ) +
                                                        (selectedUserOrderOthersTakeaway.reduce((accum, order) =>
                                                            accum + ((order && order.finalPriceBefore) ? order.finalPriceBefore : 0), 0)
                                                        )
                                                    )
                                                ).toFixed(2)
                                            }</Text>
                                        </View>

                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text style={styles.totalPrice}>RM </Text>
                                            {/* <Text style={styles.totalPrice}>{(Math.ceil((selectedUserOrderTakeaway.totalPrice + selectedUserOrderTakeaway.tax + (selectedUserOrderTakeaway.sc ? selectedUserOrderTakeaway.sc : 0) + selectedUserOrderTakeaway.deliveryFee - selectedUserOrderTakeaway.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)}</Text> */}
                                            {/*<Text style={styles.totalPrice}>{(selectedUserOrderTakeaway.finalPrice).toFixed(2)}</Text>*/}
                                            <Text style={styles.totalPrice}>{
                                                // ((selectedUserOrderTakeaway && selectedUserOrderTakeaway.finalPrice) ? selectedUserOrderTakeaway.finalPrice : 0)
                                                (
                                                    (
                                                        (orderToShow && orderToShow.finalPrice) ? orderToShow.finalPrice : 0
                                                    ) +
                                                    (selectedUserOrderOthersTakeaway.reduce((accum, order) =>
                                                        accum + ((order && order.finalPrice) ? order.finalPrice : 0), 0)
                                                    )
                                                ).toFixed(2)
                                            }</Text>
                                        </View>
                                    </View>
                                </View>

                                <View style={{ marginBottom: 15 }}>
                                    <Text
                                        style={[
                                            {
                                                alignSelf: 'center',
                                                fontFamily: 'NunitoSans-Regular',
                                                fontSize: 20,
                                                marginTop: 30,
                                                marginBottom: 10,
                                            },
                                        ]}>
                                        Thank you for your order
                                    </Text>
                                    {/* <Barcode value={receiptInfo.id.toString() + checkOutTime.toString()} format="CODE128" width={1} height={50} />
                    <Barcode format="CODE128" width={1} height={50} /> */}
                                    <Image
                                        style={{
                                            width: 124,
                                            height: 26,
                                            alignSelf: 'center',
                                            //backgroundColor: Colors.primaryColor,
                                            marginBottom: 10,
                                        }}
                                        resizeMode="contain"
                                        source={koodoo_logo}
                                    />
                                    <Text
                                        style={[
                                            {
                                                textAlign: 'center',
                                                fontSize: 16,
                                                fontWeight: 'bold',
                                            },
                                        ]}>
                                        Powered by KooDoo
                                    </Text>
                                </View>

                                <View style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '100%',
                                    flexDirection: 'row',
                                    // marginTop: 10
                                }}>

                                    {/* <TouchableOpacity style={{
                        width: "30%",
                        height: 40,
                        backgroundColor: Colors.primaryColor,
                        borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                        flexDirection: 'row',
                        zIndex: 1000,

                    }} onPress={() => {
                        // props.navigation.goBack();
                        // props.navigation.navigate('Home');

                        props.navigation.reset({
                            index: 0,
                            routes: [{
                                name: 'Home',
                            }],
                        });
                    }}>

                        <Text
                            style={{
                                textAlign: 'center',
                                color: Colors.whiteColor, fontFamily: "NunitoSans-Regular",
                            }}>
                            Home
                        </Text>

                    </TouchableOpacity> */}

                                    {
                                        (selectedOutlet && selectedOutlet.subdomain &&
                                            (selectedOutlet.dso === undefined || selectedOutlet.dso === false)
                                            &&
                                            (
                                                global.selectedOutlet && global.selectedOutlet.subdomain &&
                                                (
                                                    global.selectedOutlet.dso === undefined ||
                                                    global.selectedOutlet.dso === false
                                                )
                                            )
                                        )
                                            ?
                                            <>
                                                {
                                                    toRenderOrders.reduce(
                                                        (accu, order) =>
                                                            accu.concat(
                                                                order.cartItems
                                                                    // .filter(
                                                                    //     (cartItem) =>
                                                                    //         cartItem.deliveredAt === null,
                                                                    // )
                                                                    .map(cartItem => ({
                                                                        ...cartItem,
                                                                        userOrderId: order.uniqueId,

                                                                        userName: order.userName ? order.userName : 'N/A',
                                                                        orderId: order.orderId,
                                                                    })),
                                                            ),
                                                        [],
                                                    )
                                                        .find(item => {
                                                            if (item.priceToPay !== undefined && item.priceToPay === item.price) {
                                                                return false;
                                                            }
                                                            else {
                                                                // check if the order is the orders that already joined with as bill/new order

                                                                if ((item.priceToPay === undefined ||
                                                                    (item.priceToPay !== undefined && item.priceToPay === item.price))) {
                                                                    return true;
                                                                }
                                                                else {
                                                                    return false;
                                                                }
                                                            }
                                                        })
                                                        ?
                                                        <TouchableOpacity style={{
                                                            width: "25%",
                                                            height: 40,
                                                            backgroundColor: Colors.primaryColor,
                                                            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                                            flexDirection: 'row',
                                                            marginLeft: 10,
                                                            zIndex: 1000,
                                                        }}
                                                            disabled={
                                                                isLoading ||
                                                                !(toRenderOrders.filter((o) => o.paymentDetails === null))
                                                            }
                                                            onPress={async () => {
                                                                let dsoStored = false;
                                                                const dsoRaw = await idbGet(`dso`);
                                                                if (dsoRaw) {
                                                                    const dsoParsed = JSON.parse(dsoRaw);

                                                                    if (typeof dsoParsed === 'boolean') {
                                                                        dsoStored = dsoParsed;
                                                                    }
                                                                }

                                                                if (
                                                                    global.selectedOutlet && global.selectedOutlet.subdomain &&
                                                                    (
                                                                        global.selectedOutlet.dso === undefined ||
                                                                        global.selectedOutlet.dso === false
                                                                    )
                                                                    &&
                                                                    (
                                                                        dsoStored === undefined ||
                                                                        dsoStored === false
                                                                    )
                                                                ) {
                                                                    TableStore.update(s => {
                                                                        s.selectedPaymentMethodRemarks = '';
                                                                    });

                                                                    proceedToPaymentPage(false, true, false);
                                                                }
                                                                else {
                                                                    Alert.alert('Info', 'Online payment options is not available for now');
                                                                }
                                                            }}
                                                        >
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5 }}>
                                                                {isPay
                                                                    ? 'LOADING...'
                                                                    :
                                                                    toRenderOrders.filter((o) => o.paymentDetails === null)
                                                                        ? 'PAY'
                                                                        : 'PAID'
                                                                }
                                                            </Text>
                                                        </TouchableOpacity>
                                                        :
                                                        <></>
                                                }
                                            </>
                                            :
                                            <>
                                            </>
                                    }

                                    <TouchableOpacity style={{
                                        width: "25%",
                                        height: 40,
                                        backgroundColor: Colors.primaryColor,
                                        borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                        flexDirection: 'row',
                                        marginLeft: 10,
                                        zIndex: 1000,
                                    }}
                                        onPress={async () => {
                                            const subdomain = await AsyncStorage.getItem("latestSubdomain");

                                            if (!subdomain) {
                                                linkTo && linkTo(`${prefix}/outlet/menu`);
                                            } else {
                                                linkTo && linkTo(`${prefix}/outlet/${subdomain}/menu`);
                                            }
                                        }}
                                    >
                                        <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5 }}>MENU</Text>
                                    </TouchableOpacity>

                                    {/* <TouchableOpacity style={{
                                    width: "25%",
                                    height: 40,
                                    backgroundColor: Colors.primaryColor,
                                    borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                    flexDirection: 'row',
                                    marginLeft: 10,
                                    zIndex: 1000,
                                }}
                                    onPress={() => {
                                        if (orderId && orderData) {
                                            // props.navigation.navigate("ConfirmOrder", {
                                            //     orderResult: selectedUserOrderTakeaway,
                                            //     outletData: orderOutlet,
                                            // });

                                            // navigation.navigate("OrderStatus", { outletData: orderOutlet });
                                        }

                                        setExpandDetails(!expandDetails);

                                        setTimeout(() => {
                                            scrollViewRef && scrollViewRef.current && scrollViewRef.current.scrollToEnd({
                                                duration: 500,
                                                animated: true,
                                            });
                                        }, 100);
                                    }}
                                >
                                    <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5 }}>STATUS</Text>
                                    <Feather name={expandDetails ? "chevron-up" : "chevron-down"} size={24} color={'#ffffff'} />                                    
                                </TouchableOpacity> */}

                                    {
                                        selectedUserOrderTakeaway && selectedUserOrderTakeaway.courierActionPending
                                            // true
                                            ?
                                            <TouchableOpacity style={{
                                                width: "22%",
                                                height: 40,
                                                backgroundColor: Colors.primaryColor,
                                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                                flexDirection: 'row',
                                                zIndex: 1000,
                                                marginLeft: 10,
                                            }}
                                                onPress={() => {
                                                    setActionModalVisibility(true)
                                                }}
                                            >
                                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginRight: 5 }}>Action</Text>

                                            </TouchableOpacity>
                                            :
                                            <></>
                                    }

                                    {/* {
                        selectedUserOrderTakeaway && selectedUserOrderTakeaway.orderStatus !== USER_ORDER_STATUS.ORDER_COMPLETED && selectedUserOrderTakeaway.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT
                            ?

                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    // setCompleteModalVisibility(true)
                                    completeUserOrderByUser();
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                            :
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    //This is just for showing how UI appear
                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Received</Text>

                            </TouchableOpacity>
                    } */}
                                </View>

                                {selectedOutlet && selectedOutlet.ei === true && (
                                    <View style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: '100%',
                                        flexDirection: 'row',
                                        marginTop: 10,
                                    }}>
                                        <TouchableOpacity style={{
                                            width: "50%",
                                            height: 40,
                                            backgroundColor: Colors.primaryColor,
                                            borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                            flexDirection: 'row',
                                            marginLeft: 10,
                                            zIndex: 1000,
                                        }}
                                            onPress={async () => {
                                                const subdomain = await AsyncStorage.getItem("latestSubdomain");
                                                const orderUniqueId = orderToShow?.uniqueId || selectedUserOrderTakeaway?.uniqueId;

                                                if (!subdomain) {
                                                    linkTo && linkTo(`${prefix}/outlet/einvoice/${orderUniqueId}`);
                                                } else {
                                                    linkTo && linkTo(`${prefix}/outlet/${subdomain}/einvoice/${orderUniqueId}`);
                                                }
                                            }}
                                        >
                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", marginHorizontal: 5 }}>Request e-Invoice</Text>
                                        </TouchableOpacity>
                                    </View>
                                )}
                                {/* {
                    selectedUserOrderTakeaway && selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY
                        ?
                        <View style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            width: '100%',
                            flexDirection: 'row',
                            marginTop: 10
                        }}>
                            <TouchableOpacity style={{
                                width: "30%",
                                height: 40,
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor,
                                flexDirection: 'row',
                                zIndex: 1000,
                                marginLeft: 10,
                            }}
                                onPress={() => {
                                    props.navigation.navigate("DeliveryInfo", { orderId: orderId });

                                }}
                            >
                                <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular", }}>Delivery Info</Text>

                            </TouchableOpacity>

                        </View>
                        :
                        <></>
                } */}

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={receiptModal}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainerReceipt}>
                                        <ScrollView>
                                            <View style={[styles.modalViewReceipt, {
                                            }]}>
                                                <View style={{ borderWidth: 0, borderColor: Colors.fieldtBgColor, height: '90%', width: '90%' }}>
                                                    <View style={{ alignItems: 'center', flexDirection: 'row', justifyContent: 'center' }}>
                                                        <View style={{ alignSelf: 'center' }}>
                                                            <AsyncImage
                                                                source={{ uri: selectedUserOrderTakeaway ? selectedUserOrderTakeaway.merchantLogo : '' }}
                                                                // item={selectedUserOrderTakeaway}
                                                                style={{ width: 70, height: 70, borderRadius: 10 }}
                                                            />
                                                        </View>
                                                    </View>
                                                    <View style={styles.titleDetail}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 12 }}>{selectedUserOrderTakeaway && selectedUserOrderTakeaway.outletName}</Text>
                                                        </View>
                                                        <Text
                                                            style={{
                                                                marginTop: 10,
                                                                textAlign: 'center',
                                                                fontFamily: "NunitoSans-Bold",
                                                                fontSize: 10
                                                            }}>
                                                            {orderOutlet.address}
                                                        </Text>
                                                        <Text style={{
                                                            marginTop: 0, fontFamily: "NunitoSans-Bold", fontSize: 10
                                                        }}>
                                                            Phone: {orderOutlet.phone}
                                                        </Text>
                                                    </View>
                                                    <View style={[styles.orderTitle, { flexDirection: 'row', justifyContent: 'space-between' }]}>
                                                        <View style={{ flexDirection: 'row', width: '30%', justifyContent: 'flex-start' }}>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-SemiBold",
                                                                marginBottom: 10,
                                                            }]}>
                                                                Order # :{' '}
                                                            </Text>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-Bold",
                                                                marginBottom: 10,
                                                                /* width: '30%' */
                                                            }]} numberOfLines={2}>
                                                                {selectedUserOrderTakeaway && selectedUserOrderTakeaway.orderId}
                                                            </Text>
                                                        </View>
                                                        <View style={{ flexDirection: 'row', width: '56%', justifyContent: 'flex-end' }}>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-SemiBold",
                                                                marginBottom: 10,
                                                            }]}>
                                                                Order Type :{' '}
                                                            </Text>
                                                            <Text style={[orderCodeFontSize1, {
                                                                fontFamily: "NunitoSans-Bold",
                                                                marginBottom: 10,
                                                                /* width: '70%' */
                                                            }]} numberOfLines={2}>
                                                                {ORDER_TYPE_PARSED[selectedUserOrderTakeaway && selectedUserOrderTakeaway.orderType]}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                    <View style={{ minHeight: Dimensions.get('screen').height * 0.2, }}>
                                                        {selectedUserOrderTakeaway && selectedUserOrderTakeaway.cartItems != null
                                                            ? selectedUserOrderTakeaway.cartItems.map((element, index) => {
                                                                return (
                                                                    <View>
                                                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 5, }}>
                                                                            <View style={{ width: "60%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.name}</Text>
                                                                            </View>
                                                                            <View style={{ width: "10%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}>X{element.quantity}</Text>
                                                                            </View>
                                                                            <View style={{ width: "5%" }}>
                                                                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                            </View>
                                                                            <View style={{ width: "25%" }}>
                                                                                <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}>{element.price.toFixed(2)}</Text>
                                                                            </View>
                                                                        </View>

                                                                        {element.remarks && element.remarks.length > 0 ?
                                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                                <View style={{ width: "60%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}><Text style={{ color: '#a8a8a8' }}>- </Text>{element.remarks}</Text>
                                                                                </View>
                                                                                <View style={{ width: "10%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 10 }}></Text>
                                                                                </View>
                                                                                <View style={{ width: "5%" }}>
                                                                                    <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                                </View>
                                                                                <View style={{ width: "25%" }}>
                                                                                    <Text style={{ alignSelf: 'flex-end', fontFamily: "NunitoSans-Bold", fontSize: 10 }}></Text>
                                                                                </View>
                                                                            </View>
                                                                            : <></>
                                                                        }

                                                                    </View>
                                                                );
                                                            })
                                                            : null}
                                                    </View>
                                                    <View style={styles.totalContainer}>
                                                        <View style={{ flex: 1, width: '75%' }}>
                                                            <Text style={styles.PromotionTitleReceipt}>Voucher Applied</Text>
                                                            <Text style={styles.promotionDescriptionReceipt}> - 10% Discount OFF</Text>
                                                            <Text style={styles.descriptionReceipt}>Subtotal</Text>
                                                            <Text style={styles.descriptionReceipt}>Discount</Text>
                                                            {/* <Text style={styles.descriptionReceipt}> */}
                                                            {/* Tax ( */}
                                                            {/* {orderTax ? orderTax.rate : 0} */}
                                                            {/* {orderOutlet.taxPercent} */}
                                                            {/* %) */}
                                                            {/* </Text> */}
                                                            {
                                                                selectedOutlet && selectedOutlet.taxActive
                                                                    ?
                                                                    <Text
                                                                        style={[
                                                                            styles.description,
                                                                        ]}>
                                                                        {`Tax (${(selectedOutlet.taxRate * 100).toFixed(0)}%) ${selectedOutlet.taxNum ? `(${selectedOutlet.taxNum})` : ''}`}
                                                                    </Text>
                                                                    :
                                                                    <></>
                                                            }
                                                            {
                                                                selectedOutlet && selectedOutlet.scActive
                                                                    ?
                                                                    <Text
                                                                        style={[
                                                                            styles.description,
                                                                        ]}>
                                                                        {`${selectedOutlet.scName ? selectedOutlet.scName : 'Service Charge'} (${(selectedOutlet.scRate * 100).toFixed(0)}%)`}
                                                                    </Text>
                                                                    :
                                                                    <></>
                                                            }
                                                            <Text style={styles.descriptionReceipt}>
                                                                Delivery Fee
                                                            </Text>
                                                            <Text style={styles.totalReceipt}>Total</Text>
                                                        </View>
                                                        <View style={{ alignSelf: 'flex-end', width: '25%' }}>
                                                            {/* <Text style={styles.price}>RM {selectedUserOrderTakeaway.subtotal}</Text>
                        <Text style={styles.price}> {orderId.discount}</Text>
                        <Text style={styles.price}>
                            {' '}
                            {orderTax
                                ? (((parseFloat(orderId.subtotal) - parseFloat(orderId.discount)))
                                    * parseFloat(orderTax.rate)).toFixed(2)
                                : 0}
                        </Text>
                        <Text style={styles.totalPrice}>
                            RM {orderId.total}
                        </Text> */}
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrderTakeaway.totalPrice.toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(selectedUserOrderTakeaway && selectedUserOrderTakeaway.totalPrice) ? parseFloat(selectedUserOrderTakeaway.totalPrice).toFixed(2) : (0).toFixed(2)}</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{(selectedUserOrderTakeaway.discount).toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{
                                                                    (((selectedUserOrderTakeaway && selectedUserOrderTakeaway.discount) ? parseFloat(selectedUserOrderTakeaway.discount) : 0) + (selectedUserOrderTakeaway.discount ? selectedUserOrderTakeaway.discount : 0)).toFixed(2)
                                                                }</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrderTakeaway.tax.toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(selectedUserOrderTakeaway && selectedUserOrderTakeaway.tax) ? parseFloat(selectedUserOrderTakeaway.tax).toFixed(2) : (0).toFixed(2)}</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                                                                <Text style={styles.priceReceipt}>RM </Text>
                                                                {/*<Text style={styles.priceReceipt}>{selectedUserOrderTakeaway.deliveryFee.toFixed(2)}</Text>*/}
                                                                <Text style={styles.priceReceipt}>{(selectedUserOrderTakeaway && selectedUserOrderTakeaway.deliveryFee) ? parseFloat(selectedUserOrderTakeaway.deliveryFee).toFixed(2) : (0).toFixed(2)}</Text>
                                                            </View>
                                                            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                                                <Text style={styles.totalPriceReceipt}>RM </Text>
                                                                <Text style={styles.totalPriceReceipt}>
                                                                    {/* {(Math.ceil((selectedUserOrderTakeaway.totalPrice + selectedUserOrderTakeaway.tax + selectedUserOrderTakeaway.deliveryFee - selectedUserOrderTakeaway.discount.toFixed(2)) * 20 - 0.05) / 20).toFixed(2)} */}
                                                                    {/*(selectedUserOrderTakeaway.finalPrice).toFixed(2)*/}
                                                                    {(selectedUserOrderTakeaway && selectedUserOrderTakeaway.finalPrice) ? parseFloat(selectedUserOrderTakeaway.finalPrice).toFixed(2) : (0).toFixed(2)}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </View>
                                                </View>
                                                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', padding: 20 }}>
                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 40,
                                                        backgroundColor: Colors.whiteColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                    }}
                                                        onPress={() => {
                                                            setReceiptModal(false)
                                                        }}
                                                    >
                                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.primaryColor }}>CANCEL</Text>

                                                    </TouchableOpacity>
                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 40,
                                                        backgroundColor: Colors.primaryColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                        marginLeft: 10,
                                                    }}

                                                        onPress={() => {

                                                        }}
                                                    >
                                                        <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor }}>DOWNLOAD</Text>

                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        </ScrollView>
                                    </View>
                                </Modal>

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={actionModalVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView, {
                                        }]}>
                                            <View style={{ justifyContent: 'flex-end', alignItems: 'flex-end', padding: 6 }}>
                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: '#EB5757',
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                    marginLeft: 10,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        //cancelAndRefund()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                                    }

                                                </TouchableOpacity>
                                            </View>

                                            <View style={{
                                                flexDirection: 'column',
                                                width: '100%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                marginTop: 15,
                                                borderBottomWidth: 1,
                                                borderBottomColor: Colors.fieldtTxtColor,
                                                height: '60%',
                                            }}>

                                                <TouchableOpacity style={{
                                                    width: "65%",
                                                    height: 45,
                                                    backgroundColor: Colors.primaryColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,

                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        waitForSender()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>Wait For Sender</Text>
                                                    }


                                                </TouchableOpacity>

                                                <View style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    width: '100%',
                                                    justifyContent: 'space-between',
                                                    marginTop: 20,
                                                }}>
                                                    <View style={{
                                                        width: '50%',
                                                    }}>
                                                        <Picker
                                                            placeholder={{}}
                                                            style={Styles.rnPickerSelectStyle}
                                                            items={SENDER_DROPDOWN_LIST}
                                                            onValueChange={value => {
                                                                setSelectedSender(value);
                                                            }}
                                                        />
                                                    </View>

                                                    <TouchableOpacity style={{
                                                        width: "45%",
                                                        height: 45,
                                                        backgroundColor: Colors.primaryColor,
                                                        borderRadius: 10,
                                                        justifyContent: "center",
                                                        alignItems: "center",
                                                        borderWidth: 1,
                                                        borderColor: Colors.descriptionColor,
                                                        flexDirection: 'row',
                                                        zIndex: 1000,
                                                        // marginTop: 10,
                                                    }}
                                                        disabled={isLoading}
                                                        onPress={() => {
                                                            topup();
                                                        }}
                                                    >
                                                        {
                                                            isLoading
                                                                ?
                                                                <ActivityIndicator style={{
                                                                }} color={Colors.whiteColor} size={'small'} />
                                                                :
                                                                selectedUserOrderTakeaway ? <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>{`Top-Up (RM${(Math.max(deliveryQuotation.totalFee - selectedUserOrderTakeaway.deliveryFee, 0).toFixed(2))})`}</Text> : <></>
                                                        }
                                                    </TouchableOpacity>
                                                </View>
                                            </View>

                                            <View style={{
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                margin: 15,
                                            }}>

                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,

                                                }}
                                                    disabled={isLoading} celAnd
                                                    onPress={() => {
                                                        setActionModalVisibility(false);
                                                    }}
                                                >
                                                    <Text style={{ color: Colors.primaryColor, fontFamily: "NunitoSans-Regular" }}>CANCEL</Text>

                                                </TouchableOpacity>

                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: '#EB5757',
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                    marginLeft: 10,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        //cancelAndRefund()
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>REFUND</Text>
                                                    }

                                                </TouchableOpacity>

                                            </View>
                                        </View>
                                    </View>

                                </Modal>


                                <Modal
                                    style={{
                                    }}
                                    visible={questionnaireVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView1]}>
                                            <FlatList
                                                style={{}}
                                                data={renderQuestionnaire}
                                                //extraData={renderQuestionnaire}
                                                renderItem={renderQuestionnaire}
                                                keyExtractor={(item, index) => String(index)}
                                            />

                                        </View>
                                    </View>
                                </Modal>

                                <Modal
                                    style={{
                                        // flex: 1
                                    }}
                                    visible={completeModalVisibility}
                                    transparent={true}
                                    animationType={'slide'}
                                >
                                    <View style={styles.modalContainer}>
                                        <View style={[styles.modalView1, {}]}>
                                            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'flex-end', padding: 10 }}>
                                                <TouchableOpacity
                                                    style={styles.closeButton}
                                                    onPress={() => {
                                                        setCompleteModalVisibility(false);
                                                    }}>
                                                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                                                <Text style={{
                                                    fontSize: 20,
                                                    fontFamily: "NunitoSans-Bold"
                                                }}>
                                                    Feedback
                                                </Text>
                                                <Text style={{
                                                    fontSize: 13,
                                                    fontFamily: "NunitoSans-Regular",
                                                    marginTop: 10
                                                }}>
                                                    How was your experience in {selectedUserOrderTakeaway ? selectedUserOrderTakeaway.outletName : ''}?
                                                </Text>
                                            </View>

                                            <View style={{ flexDirection: 'row', marginTop: 10 }}>
                                                {/* <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor}/>
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                        
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity>

                    <TouchableOpacity
                    onPress={() => {
                       
                    }}
                    >
                    <AntDesign name="star" size={35} color={Colors.primaryColor} />
                    </TouchableOpacity> */}
                                                <StarRatingView />
                                            </View>

                                            <View style={{ alignItems: 'flex-start', justifyContent: 'flex-start', marginTop: 20 }}>
                                                <Text style={{
                                                    fontSize: 13,
                                                    fontFamily: "NunitoSans-Regular"
                                                }}>
                                                    Give some feedback in words.
                                                </Text>
                                            </View>

                                            <View style={{ marginTop: 5 }}>
                                                <TextInput
                                                    //editable={}
                                                    multiline={true}
                                                    clearButtonMode="while-editing"
                                                    style={styles.textInput}
                                                    placeholder="Leave Your Comment..."
                                                    onChangeText={(text) => { setReviewComment(text) }}
                                                    defaultValue={reviewComment}
                                                />
                                            </View>
                                            {/* <View style={{ marginTop: 10, alignItems: 'center' }}>
                                <Text style={{
                                    fontSize: 12.5,
                                    fontFamily: "NunitoSans-Regular",
                                }}>
                                    ( {reviewTotal} ) Ratings
                                </Text>
                            </View> */}


                                            <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', marginTop: 25 }}>
                                                <TouchableOpacity style={{
                                                    width: "47%",
                                                    height: 40,
                                                    backgroundColor: Colors.primaryColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                    borderWidth: 1,
                                                    borderColor: Colors.descriptionColor,
                                                    flexDirection: 'row',
                                                    zIndex: 1000,
                                                }}
                                                    disabled={isLoading}
                                                    onPress={() => {
                                                        submitReview()
                                                        //setQuestionnaireVisibility(true)
                                                    }}
                                                >
                                                    {
                                                        isLoading
                                                            ?
                                                            <ActivityIndicator style={{
                                                            }} color={Colors.whiteColor} size={'small'} />
                                                            :
                                                            <Text style={{ color: Colors.whiteColor, fontFamily: "NunitoSans-Regular" }}>SUBMIT</Text>
                                                    }

                                                </TouchableOpacity>
                                            </View>

                                        </View>
                                    </View>
                                </Modal>

                                {expandDetails &&
                                    <>
                                        {
                                            (
                                                !orderDetails
                                                // 2022-10-12 - Hide the following condition first
                                                // ||
                                                // (
                                                //     orderDetails &&
                                                //     orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                                //     orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                                // )
                                            )
                                                ?
                                                <View style={{
                                                    alignSelf: "center",
                                                    // marginTop: 80,
                                                    // height: Dimensions.get('screen').height * 0.75
                                                }}>
                                                    {/* <Image style={styles.headerLogo} resizeMode="contain" source={selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY ? ORDER_STATUS_IMAGES_DELIVERY[parsedOrderStatus] : ORDER_STATUS_IMAGES[parsedOrderStatus]} /> */}

                                                    <View style={[detailsTopSpacing, {
                                                        alignItems: "center",
                                                        justifyContent: 'center',
                                                        //top: '48%',
                                                        // backgroundColor: 'red',
                                                    }]}>
                                                        <Text style={[detailsTextScale, {
                                                            //fontSize: 25,
                                                            // fontWeight: "700",
                                                            color: Colors.blackColor,
                                                            fontFamily: 'NunitoSans-SemiBold',
                                                            width: '100%',
                                                            textAlign: 'center',
                                                            // marginTop: 5,
                                                            // zIndex: 10,
                                                            // backgroundColor: 'red',
                                                            width: '100%',
                                                            marginTop: 0,
                                                        }]}>
                                                            {
                                                                `#${selectedUserOrderTakeaway.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${selectedUserOrderTakeaway.orderId}:\n${selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY ?
                                                                    ORDER_STATUS_TEXT_DELIVERY[parsedOrderStatus] :
                                                                    ORDER_STATUS_TEXT[parsedOrderStatus]}`
                                                            }
                                                        </Text>

                                                        <>
                                                            {
                                                                selectedUserOrderOthersTakeaway && selectedUserOrderOthersTakeaway.map((order) => {
                                                                    return (
                                                                        <Text style={[detailsTextScale, {
                                                                            //fontSize: 25,
                                                                            // fontWeight: "700",
                                                                            color: Colors.blackColor,
                                                                            fontFamily: 'NunitoSans-SemiBold',
                                                                            width: '100%',
                                                                            textAlign: 'center',
                                                                            // marginTop: 5,
                                                                            // zIndex: 10,
                                                                            // backgroundColor: 'red',
                                                                            width: '100%',
                                                                            marginTop: 20,
                                                                        }]}>
                                                                            {
                                                                                `#${order.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${order.orderId}:\n${order.orderType === ORDER_TYPE.DELIVERY ?
                                                                                    ORDER_STATUS_TEXT_DELIVERY[parsedOrderStatusDict[order.uniqueId]] :
                                                                                    (ORDER_STATUS_TEXT[parsedOrderStatusDict[order.uniqueId]]) ? (ORDER_STATUS_TEXT[parsedOrderStatusDict[order.uniqueId]]) : (ORDER_STATUS_TEXT[ORDER_STATUS_PARSED.PLACED])}`
                                                                            }
                                                                        </Text>
                                                                    );
                                                                })
                                                            }
                                                        </>


                                                        {/* <Text style={[detailsTextScale2, {
                                                        //fontSize: 16,
                                                        marginTop: 30,
                                                        color: "#b5b5b5",
                                                        marginBottom: 50,
                                                        fontFamily: 'NunitoSans-Regular'
                                                    }]}>
                                                        {parsedOrderStatus !== ORDER_STATUS_PARSED.DELIVERED ? `Estimate time ${(((selectedUserOrderTakeaway.totalPrepareTime ? selectedUserOrderTakeaway.totalPrepareTime : 60) / 60) + 30).toFixed(0)}mins` : 'Delivered'}
                                                    </Text> */}
                                                    </View>

                                                    {/* {selectedUserOrderTakeaway.orderType === ORDER_TYPE.DELIVERY &&
                                        // <View style={{
                                        //     // backgroundColor: 'blue',
                                        // }}>
                                        <>
                                            <Image style={{
                                                // width: Styles.width * 0.9,
                                                width: '100%',
                                                // backgroundColor: 'red',

                                                // height: Dimensions.get('screen').width * 2.5,

                                                position: 'absolute',
                                                //bottom: '-2%',
                                                alignSelf: 'center',
                                            }}
                                                resizeMode="contain"
                                                source={ORDER_STATUS_IMAGES_BOTTOM[parsedOrderStatus]}
                                            />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                position: "absolute",
                                                height: Dimensions.get('screen').width * 2.7,
                                                //bottom: '-105%',
                                                //backgroundColor: 'red',
                                                //position: 'absolute',
                                                alignSelf: 'center',
                                                width: '107%',
                                            }}>
                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Placed</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Picking-up</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Delivering</Text>

                                                <Text style={{
                                                    fontSize: 12,
                                                    marginTop: 20,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '20%',
                                                    textAlign: 'center',
                                                }}>Arrived</Text>
                                            </View>
                                        </>
                                        // </View>
                                    }

                                    {(selectedUserOrderTakeaway.orderType === ORDER_TYPE.PICKUP || selectedUserOrderTakeaway.orderType === ORDER_TYPE.DINEIN) &&
                                        <>

                                            <Image style={[styles.headerLogo1, {
                                                width: Dimensions.get('screen').width,
                                                // marginLeft: 20
                                                // backgroundColor: 'red'
                                            }]} resizeMode="contain" source={ORDER_STATUS_IMAGES_BOTTOM_TAKEAWAY[parsedOrderStatus]} />

                                            <View style={{
                                                flexDirection: "row",
                                                alignItems: "center",
                                                bottom: '-115%',
                                                // backgroundColor: 'red',

                                                justifyContent: 'center',
                                                // width: '100%',
                                                width: Dimensions.get('screen').width,
                                            }}>
                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Order Placed</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Preparing</Text>

                                                <Text style={{
                                                    fontSize: 14,
                                                    marginTop: 10,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    width: '33.3%',
                                                    textAlign: 'center',
                                                }}>Done</Text>
                                            </View>
                                        </>
                                    } */}
                                                </View>
                                                :
                                                <></>
                                        }

                                        {
                                            orderDetails
                                                ?
                                                <View style={[{
                                                    width: '100%',
                                                }, (
                                                    orderDetails &&
                                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
                                                    orderDetails.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
                                                ) ? {

                                                } : {
                                                    marginTop: '10%',
                                                }]}>
                                                    <Text style={{
                                                        fontSize: 16,
                                                        // marginTop: 10,
                                                        color: Colors.secondaryColor,
                                                        fontFamily: 'NunitoSans-SemiBold',
                                                        textAlign: 'center',
                                                    }}>{
                                                            `Remark: ${orderDetails.message}`
                                                        }</Text>
                                                </View>
                                                :
                                                <></>
                                        }

                                        <View style={{ height: 120 }}></View>
                                    </>
                                }
                            </View>
                        </ScrollView>
                    </>
                    :
                    <View style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                        <Text style={{
                            fontSize: 16,
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-SemiBold',
                            textAlign: 'center',
                        }}>{
                                `No active takeaways found.`
                            }</Text>
                    </View>
            }
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        paddingTop: 5,
    },
    titleDetail: {
        marginTop: 5,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
    },
    orderTitle: {
        marginTop: 10,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: Colors.fieldtTxtColor,
    },
    totalContainer: {
        flexDirection: 'row',
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: Colors.fieldtTxtColor,
        // marginTop: 10,
    },
    description: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
    },
    descriptionReceipt: {
        paddingVertical: 5,
        fontSize: 12,
        fontFamily: "NunitoSans-Bold",
    },
    PromotionTitle: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    PromotionTitleReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor
    },
    price: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    priceReceipt: {
        paddingVertical: 5,
        fontSize: 10,
        alignSelf: 'flex-end',
        fontFamily: "NunitoSans-Bold",
    },
    promotionDescription: {
        paddingVertical: 5,
        fontSize: 13,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    promotionDescriptionReceipt: {
        paddingVertical: 2,
        fontSize: 10,
        fontFamily: "NunitoSans-Bold",
        color: Colors.fieldtTxtColor,
    },
    totalPrice: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalPriceReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    total: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalReceipt: {
        paddingVertical: 5,
        fontSize: 15,
        fontWeight: '700',
        marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },

    outletCover: {
        width: "100%",
        alignSelf: 'center',
        height: undefined,
        aspectRatio: 2,
        borderRadius: 5
    },
    infoTab: {
        backgroundColor: Colors.fieldtBgColor,
        padding: 16,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center'
    },
    workingHourTab: {
        padding: 16,
        flexDirection: 'row'
    },
    outletAddress: {
        textAlign: 'center',
        color: Colors.mainTxtColor
    },
    outletName: {
        fontWeight: 'bold',
        fontSize: 20,
        marginBottom: 10
    },
    logo: {
        width: 100,
        height: 100
    },
    headerLogo: {
        width: Styles.width * 0.8,
        //height: '100%'
        // backgroundColor: 'red',
        position: 'absolute',
        top: '-95%',
        alignSelf: 'center',
    },
    headerLogo1: {
        // width: Styles.width * 0.9,
        width: '100%',
        // backgroundColor: 'red',
        position: 'absolute',
        bottom: '-2%',
        alignSelf: 'center',
    },
    actionTab: {
        flexDirection: 'row',
        marginTop: 20
    },
    actionView: {
        width: Styles.width / 4,
        height: Styles.width / 4,
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center'
    },
    actionBtn: {
        borderRadius: 50,
        width: 70,
        height: 70,
        borderColor: Colors.secondaryColor,
        borderWidth: StyleSheet.hairlineWidth,
        justifyContent: 'center',
        alignItems: 'center'
    },
    actionText: {
        fontSize: 12,
        marginTop: 10
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView: {
        height: Styles.width * 0.7,
        width: Styles.width * 0.8,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    modalContainerReceipt: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalViewReceipt: {
        //height: Styles.height * 0.9,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        //padding: Styles.width * 0.03,
        alignItems: 'center',
        justifyContent: 'center'
    },
    modalView1: {
        //height: Styles.width * 1,
        width: Styles.width * 0.85,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Styles.width * 0.03,
        //alignItems: 'center',
        //justifyContent: 'center'
    },
    closeButton: {
        position: 'absolute',
        right: Styles.width * 0.02,
        top: Styles.width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    textInput: {
        height: 150,
        paddingHorizontal: 5,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginTop: 10,
        textAlignVertical: "top",
        fontSize: 13
    },
    starStyle: {
        width: 35,
        height: 35,
    }
});

export default OrderDetailScreen;
